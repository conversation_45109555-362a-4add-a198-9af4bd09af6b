/**
 * 任务提醒管理器
 * 负责检查任务提醒时间并触发通知
 * 支持页面可见性检测和智能生命周期管理
 */
class TaskReminderManager {
    constructor(todoManager) {
        this.todoManager = todoManager;
        this.checkInterval = null;
        this.checkIntervalMs = 60000; // 每分钟检查一次
        this.triggeredReminders = new Set(); // 记录已触发的提醒
        this.storageKey = 'todo_triggered_reminders'; // 持久化存储key

        // 页面可见性和生命周期管理
        this.isPageVisible = !document.hidden;
        this.isTodoPageActive = false;
        this.isDestroyed = false;

        // 绑定页面可见性事件
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);

        console.log('TaskReminderManager 初始化');
        this.init();
    }

    /**
     * 初始化提醒管理器
     */
    async init() {
        // 加载已触发的提醒记录
        this.loadTriggeredReminders();

        // 绑定页面可见性事件
        this.bindVisibilityEvents();

        // 监听 todo 页面状态变化
        this.bindTodoPageEvents();

        // 检测页面刷新时是否已在 todo 页面
        this.detectInitialTodoPageState();

        // 根据当前状态决定是否开始检查
        this.updateCheckingState();

        console.log('TaskReminderManager 初始化完成', {
            checkInterval: this.checkIntervalMs,
            loadedReminders: this.triggeredReminders.size,
            isPageVisible: this.isPageVisible,
            isTodoPageActive: this.isTodoPageActive
        });
    }

    /**
     * 绑定页面可见性事件
     */
    bindVisibilityEvents() {
        if (typeof document.addEventListener !== 'undefined') {
            document.addEventListener('visibilitychange', this.handleVisibilityChange, false);
        }

        // 兼容性处理
        if (typeof document.addEventListener !== 'undefined') {
            document.addEventListener('webkitvisibilitychange', this.handleVisibilityChange, false);
            document.addEventListener('mozvisibilitychange', this.handleVisibilityChange, false);
            document.addEventListener('msvisibilitychange', this.handleVisibilityChange, false);
        }
    }

    /**
     * 监听 todo 页面状态变化
     */
    bindTodoPageEvents() {
        if (this.todoManager && this.todoManager.on) {
            this.todoManager.on('todo-shown', () => {
                this.isTodoPageActive = true;
                this.updateCheckingState();
                console.log('Todo 页面已显示，提醒检查状态更新');
            });

            this.todoManager.on('todo-hidden', () => {
                this.isTodoPageActive = false;
                this.updateCheckingState();
                console.log('Todo 页面已隐藏，提醒检查状态更新');
            });
        }
    }

    /**
     * 检测页面刷新时是否已在 todo 页面
     */
    detectInitialTodoPageState() {
        // 检查 todo 容器是否可见
        const todoContainer = document.querySelector('.todo-container');
        if (todoContainer) {
            const isVisible = todoContainer.style.display !== 'none' &&
                             getComputedStyle(todoContainer).display !== 'none';

            if (isVisible) {
                this.isTodoPageActive = true;
                console.log('检测到页面刷新时已在 todo 页面，激活提醒检查');
            }
        }

        // 检查 URL 哈希或其他状态指示器
        if (window.location.hash === '#todo' ||
            document.title.includes('任务管理') ||
            document.querySelector('.content-area')?.style.display === 'none') {
            this.isTodoPageActive = true;
            console.log('通过页面状态检测到已在 todo 页面');
        }
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        this.isPageVisible = !document.hidden;
        this.updateCheckingState();

        console.log('页面可见性变化:', {
            isPageVisible: this.isPageVisible,
            isTodoPageActive: this.isTodoPageActive,
            shouldCheck: this.shouldRunChecking()
        });
    }

    /**
     * 判断是否应该运行提醒检查
     */
    shouldRunChecking() {
        return this.isPageVisible && this.isTodoPageActive && !this.isDestroyed;
    }

    /**
     * 更新检查状态
     */
    updateCheckingState() {
        if (this.shouldRunChecking()) {
            this.startChecking();
        } else {
            this.stopChecking();
        }
    }

    /**
     * 开始定时检查提醒
     */
    startChecking() {
        // 如果已经在运行，不重复启动
        if (this.checkInterval) {
            return;
        }

        // 立即检查一次
        this.checkTaskReminders();

        // 设置定时检查
        this.checkInterval = setInterval(() => {
            // 在每次检查前再次验证状态
            if (this.shouldRunChecking()) {
                this.checkTaskReminders();
            } else {
                this.stopChecking();
            }
        }, this.checkIntervalMs);

        console.log('任务提醒检查已启动');
    }

    /**
     * 停止检查提醒
     */
    stopChecking() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
            console.log('任务提醒检查已停止');
        }
    }
    
    /**
     * 检查所有任务的提醒
     */
    checkTaskReminders() {
        try {
            const now = new Date();

            // 直接访问 todoManager 的 tasks 数组
            if (!this.todoManager.tasks || this.todoManager.tasks.length === 0) {
                return;
            }

            const tasks = this.todoManager.tasks;
            let checkedCount = 0;
            const triggeredTasks = [];

            // 收集所有需要触发提醒的任务
            tasks.forEach(task => {
                if (this.shouldTriggerReminder(task, now)) {
                    triggeredTasks.push(task);
                    this.markReminderTriggered(task);
                }
                if (task.reminderDate) {
                    checkedCount++;
                }
            });

            // 如果有需要提醒的任务，合并显示
            if (triggeredTasks.length > 0) {
                this.triggerBatchReminders(triggeredTasks);
            }

            if (checkedCount > 0) {
                console.log(`检查了 ${checkedCount} 个有提醒的任务，触发了 ${triggeredTasks.length} 个提醒`);
            }

        } catch (error) {
            console.error('检查任务提醒时出错:', error);
        }
    }
    
    /**
     * 判断是否应该触发提醒
     */
    shouldTriggerReminder(task, now) {
        // 基本条件检查
        if (!task.reminderDate || task.completed || task.archived) {
            return false;
        }
        
        // 检查是否已经触发过
        if (this.triggeredReminders.has(task.id)) {
            return false;
        }
        
        const reminderTime = new Date(task.reminderDate);
        const timeDiff = now.getTime() - reminderTime.getTime();
        
        // 在提醒时间后5分钟内触发（允许一定的时间窗口）
        return timeDiff >= 0 && timeDiff <= 5 * 60 * 1000;
    }
    
    /**
     * 批量触发任务提醒（合并显示）
     */
    triggerBatchReminders(tasks) {
        if (tasks.length === 0) return;

        console.log('批量触发任务提醒:', tasks.map(t => t.title));

        if (tasks.length === 1) {
            // 单个任务，使用原有逻辑
            this.triggerSingleReminder(tasks[0]);
        } else {
            // 多个任务，合并显示
            this.triggerMultipleReminders(tasks);
        }
    }

    /**
     * 触发单个任务提醒
     */
    triggerSingleReminder(task) {
        const reminderTime = new Date(task.reminderDate);
        const message = `任务"${task.title}"的提醒时间到了`;

        this.showAppNotification('📅 任务提醒', message, false); // 单个任务使用纯文本
    }

    /**
     * 触发多个任务提醒（合并显示）
     */
    triggerMultipleReminders(tasks) {
        const taskCount = tasks.length;

        // 创建格式化的任务列表HTML
        const taskListHtml = tasks.map((task, index) => {
            const taskNumber = index + 1;
            const truncatedTitle = task.title.length > 30 ?
                task.title.substring(0, 30) + '...' : task.title;

            return `
                <div style="
                    display: flex;
                    align-items: center;
                    padding: 8px 0;
                    border-bottom: 1px solid #f0f0f0;
                    ${index === tasks.length - 1 ? 'border-bottom: none;' : ''}
                ">
                    <span style="
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        width: 20px;
                        height: 20px;
                        background: #007bff;
                        color: white;
                        border-radius: 50%;
                        font-size: 12px;
                        font-weight: bold;
                        margin-right: 12px;
                        flex-shrink: 0;
                    ">${taskNumber}</span>
                    <span style="
                        flex: 1;
                        font-size: 14px;
                        line-height: 1.4;
                        color: #333;
                        word-break: break-word;
                    ">${truncatedTitle}</span>
                </div>
            `;
        }).join('');

        const title = `📅 任务提醒`;
        const message = `
            <div style="margin-bottom: 12px;">
                <strong style="color: #333; font-size: 15px;">
                    ${taskCount} 个任务的提醒时间到了
                </strong>
            </div>
            <div style="
                max-height: 200px;
                overflow-y: auto;
                background: #fafafa;
                border-radius: 6px;
                padding: 12px;
                margin: 8px 0;
            ">
                ${taskListHtml}
            </div>
            <div style="
                font-size: 12px;
                color: #666;
                text-align: center;
                margin-top: 8px;
            ">
                点击任务管理查看详情
            </div>
        `;

        this.showAppNotification(title, message, true); // 第三个参数表示使用HTML格式
    }

    /**
     * 显示应用内通知
     */
    showAppNotification(title, message, isHtml = false) {
        // 确保只在 todo 页面活跃时显示通知
        if (!this.isTodoPageActive) {
            console.log('Todo 页面未激活，跳过提醒显示');
            return;
        }

        this.todoManager.showNotification({
            title: title,
            message: message,
            type: 'warning',
            duration: 15000, // 15秒后自动关闭，给用户更多时间阅读多任务提醒
            isHtml: isHtml // 支持HTML格式
        });
    }
    
    /**
     * 标记提醒已触发
     */
    markReminderTriggered(task) {
        // 添加到内存中的Set
        this.triggeredReminders.add(task.id);

        // 持久化存储
        this.saveTriggeredReminders();

        // 定期清理已触发的提醒记录（避免内存泄漏）
        if (this.triggeredReminders.size > 1000) {
            this.cleanupTriggeredReminders();
        }
    }

    /**
     * 加载已触发的提醒记录
     */
    loadTriggeredReminders() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const data = JSON.parse(stored);

                // 检查数据格式和有效性
                if (data && data.reminders && Array.isArray(data.reminders)) {
                    // 只加载最近24小时内的提醒记录
                    const now = new Date();
                    const oneDayAgo = now.getTime() - 24 * 60 * 60 * 1000;

                    const validReminders = data.reminders.filter(item => {
                        return item.timestamp && item.timestamp > oneDayAgo;
                    });

                    this.triggeredReminders = new Set(validReminders.map(item => item.taskId));
                    console.log(`加载了 ${this.triggeredReminders.size} 个已触发的提醒记录`);
                }
            }
        } catch (error) {
            console.error('加载提醒记录失败:', error);
            this.triggeredReminders = new Set();
        }
    }

    /**
     * 保存已触发的提醒记录
     */
    saveTriggeredReminders() {
        try {
            const now = new Date();
            const reminders = Array.from(this.triggeredReminders).map(taskId => ({
                taskId: taskId,
                timestamp: now.getTime()
            }));

            const data = {
                reminders: reminders,
                lastUpdated: now.toISOString()
            };

            localStorage.setItem(this.storageKey, JSON.stringify(data));
        } catch (error) {
            console.error('保存提醒记录失败:', error);
        }
    }
    
    /**
     * 清理已触发的提醒记录
     */
    cleanupTriggeredReminders() {
        const tasks = this.todoManager.tasks || [];
        const currentTaskIds = new Set(tasks.map(task => task.id));

        // 只保留当前存在的任务的提醒记录
        const newTriggeredReminders = new Set();
        this.triggeredReminders.forEach(taskId => {
            if (currentTaskIds.has(taskId)) {
                newTriggeredReminders.add(taskId);
            }
        });

        this.triggeredReminders = newTriggeredReminders;

        // 更新持久化存储
        this.saveTriggeredReminders();

        console.log('清理提醒记录，当前记录数:', this.triggeredReminders.size);
    }
    
    /**
     * 重置特定任务的提醒状态（当任务被修改时调用）
     */
    resetTaskReminder(taskId) {
        this.triggeredReminders.delete(taskId);

        // 更新持久化存储
        this.saveTriggeredReminders();

        console.log('重置任务提醒状态:', taskId);
    }
    
    /**
     * 获取提醒统计信息
     */
    getReminderStats() {
        const tasks = this.todoManager.tasks || [];
        const tasksWithReminders = tasks.filter(task => task.reminderDate && !task.completed);
        const overdueReminders = tasks.filter(task => {
            if (!task.reminderDate || task.completed) return false;
            return new Date() > new Date(task.reminderDate);
        });

        return {
            totalTasks: tasks.length,
            tasksWithReminders: tasksWithReminders.length,
            overdueReminders: overdueReminders.length,
            triggeredCount: this.triggeredReminders.size
        };
    }
    
    /**
     * 销毁提醒管理器
     */
    destroy() {
        // 标记为已销毁
        this.isDestroyed = true;

        // 停止检查
        this.stopChecking();

        // 解绑页面可见性事件
        this.unbindVisibilityEvents();

        // 清理数据
        this.triggeredReminders.clear();

        console.log('TaskReminderManager 已销毁');
    }

    /**
     * 解绑页面可见性事件
     */
    unbindVisibilityEvents() {
        if (typeof document.removeEventListener !== 'undefined') {
            document.removeEventListener('visibilitychange', this.handleVisibilityChange, false);
            document.removeEventListener('webkitvisibilitychange', this.handleVisibilityChange, false);
            document.removeEventListener('mozvisibilitychange', this.handleVisibilityChange, false);
            document.removeEventListener('msvisibilitychange', this.handleVisibilityChange, false);
        }
    }

    /**
     * 手动设置 todo 页面状态（用于外部调用）
     */
    setTodoPageActive(isActive) {
        this.isTodoPageActive = isActive;
        this.updateCheckingState();
        console.log('手动设置 Todo 页面状态:', isActive);
    }

    /**
     * 获取当前状态信息
     */
    getStatus() {
        return {
            isPageVisible: this.isPageVisible,
            isTodoPageActive: this.isTodoPageActive,
            isDestroyed: this.isDestroyed,
            isChecking: !!this.checkInterval,
            shouldRunChecking: this.shouldRunChecking(),
            triggeredCount: this.triggeredReminders.size
        };
    }
}
