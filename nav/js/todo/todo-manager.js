/**
 * ToDo 主管理器
 * 统一管理所有 ToDo 功能，与 NavApp 集成
 */
class TodoManager {
    constructor(navApp) {
        this.navApp = navApp;
        this.storage = new TodoStorage();
        
        // 数据
        this.tasks = [];
        this.lists = [];
        this.archivedTasks = [];
        this.settings = {};
        
        // 子管理器
        this.subtaskManager = null;
        this.statusManager = null;
        this.priorityManager = null;
        this.archiveManager = null;
        this.countdownManager = null;
        this.memoManager = null;
        this.dataManager = null;
        this.reminderManager = null;
        
        // 事件监听器
        this.eventListeners = new Map();

        // 倒计时更新定时器
        this.countdownUpdateInterval = null;

        // UI 元素
        this.todoContainer = null;
        this.isVisible = false;

        // 初始化状态
        this.isInitialized = false;
        this.initPromise = null;

        // ESC键处理器引用
        this._currentEscHandler = null;

        // 对话框层级管理
        this._dialogZIndex = 10020;

        // 状态切换防抖
        this._statusChangeDebounce = new Map();

        // 开始异步初始化
        this.initPromise = this.init().catch(error => {
            console.error('ToDo 管理器初始化失败:', error);
            throw error;
        });
    }

    /**
     * 初始化 ToDo 管理器
     */
    async init() {
        try {
            console.log('开始初始化 ToDo 管理器...');

            // 加载数据
            await this.loadData();

            // 初始化子管理器
            this.initializeSubManagers();

            // 创建 UI
            this.createUI();

            // 绑定事件
            this.bindEvents();

            this.isInitialized = true;
            console.log('ToDo 管理器初始化完成');
        } catch (error) {
            console.error('ToDo 管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 等待初始化完成
     */
    async waitForInit() {
        if (this.initPromise) {
            await this.initPromise;
        }
        return this.isInitialized;
    }

    /**
     * 加载数据
     */
    async loadData() {
        const data = this.storage.getAll();

        this.tasks = data.tasks || [];
        this.lists = data.lists || [];
        this.archivedTasks = data.archivedTasks || [];
        this.settings = data.settings || {};

        // 数据迁移：将旧的priority系统迁移到四象限系统
        let migrationCount = 0;
        this.tasks.forEach(task => {
            if (task.priority && !task.quadrant) {
                const oldPriorityLevel = task.priority.level;

                // 从旧的priority转换为quadrant
                task.quadrant = TodoUtils.priorityToQuadrant(oldPriorityLevel);

                // 重新计算important和urgent
                const { important, urgent } = TodoUtils.calculateImportanceUrgency(task.quadrant);
                task.important = important;
                task.urgent = urgent;

                // 移除旧的priority字段
                delete task.priority;

                migrationCount++;
                console.log(`迁移任务 "${task.title}": priority "${oldPriorityLevel}" -> quadrant ${task.quadrant}`);
            }
        });

        if (migrationCount > 0) {
            console.log(`数据迁移完成: ${migrationCount} 个任务从priority系统迁移到四象限系统`);
            this.saveData(); // 保存迁移后的数据
        }

        // 数据迁移：修复时间格式问题
        let timeMigrationCount = 0;
        this.tasks.forEach(task => {
            let taskUpdated = false;

            // 检查并修复 dueDate 格式
            if (task.dueDate && !this.isValidISOString(task.dueDate)) {
                const convertedDueDate = this.convertLocalDateTimeToISO(task.dueDate);
                if (convertedDueDate) {
                    task.dueDate = convertedDueDate;
                    taskUpdated = true;
                    console.log(`修复任务 "${task.title}" 的截止时间格式: ${task.dueDate} -> ${convertedDueDate}`);
                }
            }

            // 检查并修复 reminderDate 格式
            if (task.reminderDate && !this.isValidISOString(task.reminderDate)) {
                const convertedReminderDate = this.convertLocalDateTimeToISO(task.reminderDate);
                if (convertedReminderDate) {
                    task.reminderDate = convertedReminderDate;
                    taskUpdated = true;
                    console.log(`修复任务 "${task.title}" 的提醒时间格式: ${task.reminderDate} -> ${convertedReminderDate}`);
                }
            }

            if (taskUpdated) {
                timeMigrationCount++;
            }
        });

        if (timeMigrationCount > 0) {
            console.log(`时间格式迁移完成: ${timeMigrationCount} 个任务的时间格式已修复`);
            this.saveData(); // 保存迁移后的数据
        }

        // 确保至少有一个默认列表
        if (this.lists.length === 0) {
            this.lists.push({
                id: TodoUtils.generateId(),
                name: '我的任务',
                icon: 'fas fa-list',
                color: '#007bff',
                isDefault: true,
                createdAt: new Date().toISOString()
            });
            this.saveData();
        }

        console.log(`加载了 ${this.tasks.length} 个任务，${this.lists.length} 个列表`);
    }
    
    /**
     * 初始化子管理器
     */
    initializeSubManagers() {
        // 初始化提醒管理器
        if (typeof TaskReminderManager !== 'undefined') {
            this.reminderManager = new TaskReminderManager(this);
            console.log('提醒管理器已初始化');
        } else {
            console.warn('TaskReminderManager 未加载，提醒功能不可用');
        }

        // 这些管理器将在后续步骤中实现
        // this.subtaskManager = new SubtaskManager(this);
        // this.statusManager = new TodoStatusManager(this);
        // this.priorityManager = new TodoPriorityManager(this);
        // this.archiveManager = new TodoArchiveManager(this);
        // this.countdownManager = new CountdownManager(this);
        // this.memoManager = new MemoManager(this);
        // this.dataManager = new TodoDataManager(this);
    }
    
    /**
     * 创建 UI
     */
    createUI() {
        // 检查是否已存在 ToDo 容器
        this.todoContainer = document.getElementById('todoContainer');
        
        if (!this.todoContainer) {
            this.todoContainer = document.createElement('div');
            this.todoContainer.id = 'todoContainer';
            this.todoContainer.className = 'todo-container';
            this.todoContainer.style.display = 'none';
            
            // 插入到主容器中，与 .content-area 同级
            const mainContainer = document.querySelector('.main-container');
            if (mainContainer) {
                mainContainer.appendChild(this.todoContainer);
            }
        }
        
        this.renderUI();
    }
    
    /**
     * 渲染 UI
     */
    renderUI() {
        if (!this.todoContainer) return;
        
        this.todoContainer.innerHTML = `
            <div class="todo-header">
                <div class="todo-title">
                    <h2><i class="fas fa-tasks"></i> 任务管理</h2>
                    <div class="todo-stats">
                        <span class="stat-item">
                            <span class="stat-number">${this.getActiveTasksCount()}</span>
                            <span class="stat-label">活动任务</span>
                        </span>
                        <span class="stat-item">
                            <span class="stat-number">${this.getCompletedTasksCount()}</span>
                            <span class="stat-label">已完成</span>
                        </span>
                    </div>
                </div>
                <div class="todo-actions">
                    <button class="btn-primary add-task-btn">
                        <i class="fas fa-plus"></i> 新建任务
                    </button>
                    <button class="btn-secondary todo-settings-btn">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
            
            <div class="todo-content">
                <div class="todo-sidebar">
                    <nav class="todo-nav">
                        <ul class="todo-nav-list">
                            <li class="nav-item active" data-view="today">
                                <i class="fas fa-calendar-day"></i>
                                <span>今天</span>
                                <span class="nav-count">${this.getTodayTasksCount()}</span>
                            </li>
                            <li class="nav-item" data-view="important">
                                <i class="fas fa-star"></i>
                                <span>重要</span>
                                <span class="nav-count">${this.getImportantTasksCount()}</span>
                            </li>
                            <li class="nav-item" data-view="urgent">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>紧急</span>
                                <span class="nav-count">${this.getUrgentTasksCount()}</span>
                            </li>
                            <li class="nav-item" data-view="planned">
                                <i class="fas fa-calendar-alt"></i>
                                <span>已计划</span>
                                <span class="nav-count">${this.getPlannedTasksCount()}</span>
                            </li>
                            <li class="nav-item" data-view="all">
                                <i class="fas fa-list"></i>
                                <span>全部任务</span>
                                <span class="nav-count">${this.getActiveTasksCount()}</span>
                            </li>
                            <li class="nav-item" data-view="archived">
                                <i class="fas fa-archive"></i>
                                <span>已归档</span>
                                <span class="nav-count">${this.getArchivedTasksCount()}</span>
                            </li>
                        </ul>
                        
                        <div class="todo-lists">
                            <div class="lists-header">
                                <h4>我的列表</h4>
                                <button class="add-list-btn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <ul class="lists-container">
                                ${this.renderLists()}
                            </ul>
                        </div>
                    </nav>
                </div>
                
                <div class="todo-main">
                    <div class="todo-view-header">
                        <h3 class="view-title">今天</h3>
                        <div class="view-actions">
                            <button class="sort-btn" title="排序">
                                <i class="fas fa-sort"></i>
                            </button>
                            <button class="filter-btn" title="筛选">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="tasks-container">
                        ${this.renderTasks('today')}
                    </div>
                </div>
            </div>
        `;
        
        this.bindUIEvents();
    }
    
    /**
     * 渲染任务列表
     */
    renderLists() {
        return this.lists.map(list => `
            <li class="nav-item list-item" data-view="list" data-list-id="${list.id}">
                <div class="list-content">
                    <i class="${list.icon}" style="color: ${list.color}"></i>
                    <span class="list-name">${list.name}</span>
                    <span class="nav-count">${this.getListTasksCount(list.id)}</span>
                </div>
                <button class="list-more-btn" data-list-id="${list.id}" title="更多选项">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </li>
        `).join('');
    }
    
    /**
     * 渲染任务
     */
    renderTasks(view = 'today', listId = null) {
        const tasks = this.getTasksByView(view, listId);
        
        if (tasks.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-tasks fa-3x"></i>
                    <h3>暂无任务</h3>
                    <p>点击"新建任务"开始添加您的第一个任务</p>
                </div>
            `;
        }
        
        return tasks.map(task => this.renderTask(task)).join('');
    }
    
    /**
     * 渲染单个任务
     */
    renderTask(task) {
        const urgency = TodoUtils.getUrgencyLevel(task);
        const progress = TodoUtils.calculateProgress(task);

        // 获取四象限优先级信息
        const quadrantInfo = task.quadrant ? TodoUtils.getQuadrantPriorityMap()[task.quadrant] : null;

        const archivedClass = task.archived ? ' archived' : '';

        return `
            <div class="task-item${archivedClass}" data-task-id="${task.id}" data-status="${task.status || 'not-started'}" data-urgency="${urgency}">
                <div class="task-status-selector" data-status="${task.status || 'not-started'}" tabindex="0" role="button" aria-label="${this.getStatusAriaLabel(task.status || 'not-started')}">
                    <div class="status-indicator" title="${this.getStatusTooltip(task.status || 'not-started')}">
                        <span class="status-icon"></span>
                    </div>
                </div>
                
                <div class="task-content">
                    <div class="task-header">
                        <h4 class="task-title">${task.title}</h4>
                        <div class="task-badges">
                            ${task.quadrant ? `
                                <div class="task-quadrant quadrant-${task.quadrant}" title="${quadrantInfo.label} - ${quadrantInfo.action}">
                                    <span class="quadrant-icon">${quadrantInfo.icon}</span>
                                    <span class="quadrant-text">${quadrantInfo.label}</span>
                                    <span class="quadrant-priority">${quadrantInfo.level.toUpperCase()}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    ${task.description ? `
                        <p class="task-description">${task.description}</p>
                    ` : ''}
                    
                    <div class="task-meta">
                        ${task.dueDate ? `
                            <span class="task-due-date ${urgency}" data-due-date="${task.dueDate}">
                                <i class="fas fa-clock"></i>
                                <span class="due-time-text">${TodoUtils.formatDueTime(task.dueDate)}</span>
                            </span>
                        ` : ''}

                        ${task.reminderDate ? `
                            <span class="task-reminder ${this.getReminderStatus(task)}" data-reminder-date="${task.reminderDate}">
                                <i class="fas fa-bell"></i>
                                <span class="reminder-time-text">${TodoUtils.formatReminderTime(task.reminderDate)}</span>
                            </span>
                        ` : ''}

                        ${task.steps && task.steps.length > 0 ? `
                            <span class="task-progress">
                                <i class="fas fa-list-ul"></i>
                                ${progress}% (${this.getCompletedStepsCount(task)}/${task.steps.length})
                            </span>
                        ` : ''}
                        
                        ${task.memo && task.memo.content ? `
                            <span class="task-memo">
                                <i class="fas fa-sticky-note"></i>
                                ${task.memo.wordCount} 字
                            </span>
                        ` : ''}
                    </div>
                </div>
                
                <div class="task-actions">
                    <button class="task-action-btn edit-btn" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="task-action-btn more-btn" title="更多">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 绑定 UI 事件
     */
    bindUIEvents() {
        if (!this.todoContainer) return;

        // 新建任务
        const addTaskBtn = this.todoContainer.querySelector('.add-task-btn');
        if (addTaskBtn) {
            addTaskBtn.addEventListener('click', () => this.showAddTaskDialog());
        }

        // 设置按钮
        const settingsBtn = this.todoContainer.querySelector('.todo-settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.showSettingsDialog());
        }

        // 添加列表按钮
        const addListBtn = this.todoContainer.querySelector('.add-list-btn');
        if (addListBtn) {
            addListBtn.addEventListener('click', () => this.showAddListDialog());
        }

        // 导航切换
        const navItems = this.todoContainer.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                // 如果点击的是更多按钮，不处理导航切换
                if (e.target.closest('.list-more-btn')) {
                    return;
                }

                const view = e.currentTarget.dataset.view;
                const listId = e.currentTarget.dataset.listId;

                if (view === 'list' && listId) {
                    this.switchToListView(listId);
                } else {
                    this.switchView(view);
                }
            });
        });

        // 列表更多选项按钮
        this.todoContainer.addEventListener('click', (e) => {
            if (e.target.closest('.list-more-btn')) {
                e.stopPropagation();
                const listId = e.target.closest('.list-more-btn').dataset.listId;
                this.showListMoreOptions(listId, e.target.closest('.list-more-btn'));
            }
        });

        // 排序按钮
        const sortBtn = this.todoContainer.querySelector('.sort-btn');
        if (sortBtn) {
            sortBtn.addEventListener('click', () => this.showSortOptions());
        }

        // 筛选按钮
        const filterBtn = this.todoContainer.querySelector('.filter-btn');
        if (filterBtn) {
            filterBtn.addEventListener('click', () => this.showFilterOptions());
        }

        // 使用事件委托处理动态生成的元素（仅处理todoContainer内的元素）
        this.todoContainer.addEventListener('click', (e) => {
            // 任务编辑按钮
            if (e.target.closest('.edit-btn')) {
                const taskId = e.target.closest('.task-item').dataset.taskId;
                this.showEditTaskDialog(taskId);
                return;
            }

            // 任务更多按钮
            if (e.target.closest('.more-btn')) {
                e.preventDefault();
                e.stopImmediatePropagation();

                const taskItem = e.target.closest('.task-item');
                const moreBtn = e.target.closest('.more-btn');

                if (taskItem && moreBtn) {
                    const taskId = taskItem.dataset.taskId;
                    console.log('点击更多按钮，任务ID:', taskId);
                    this.showTaskMoreOptions(taskId, moreBtn);
                }
                return;
            }

            // 任务删除按钮
            if (e.target.closest('.delete-btn')) {
                const taskId = e.target.closest('.task-item').dataset.taskId;
                this.deleteTask(taskId);
                return;
            }
        });

        // 任务状态选择器 - 点击事件
        this.todoContainer.addEventListener('click', (e) => {
            const statusSelector = e.target.closest('.task-status-selector');
            if (statusSelector) {
                e.preventDefault();
                e.stopPropagation();
                const taskId = statusSelector.closest('.task-item').dataset.taskId;
                this.cycleTaskStatus(taskId);
            }
        });

        // 任务状态选择器 - 键盘事件
        this.todoContainer.addEventListener('keydown', (e) => {
            // 只处理状态选择器获得焦点时的键盘事件
            if (e.target.classList.contains('task-status-selector') && (e.key === ' ' || e.key === 'Enter')) {
                e.preventDefault();
                e.stopPropagation();
                const taskId = e.target.closest('.task-item').dataset.taskId;
                this.cycleTaskStatus(taskId);
            }
        });
    }

    /**
     * 显示设置对话框
     */
    showSettingsDialog() {
        console.log('显示设置对话框');
        this.showNotification({
            title: '功能开发中',
            message: '设置功能正在开发中，敬请期待！',
            type: 'info'
        });
    }

    /**
     * 显示添加列表对话框
     */
    showAddListDialog() {
        console.log('显示添加列表对话框');
        this.showInputDialog({
            title: '创建新列表',
            message: '请输入列表名称',
            placeholder: '例如：工作任务、个人计划...',
            confirmText: '创建',
            cancelText: '取消',
            onConfirm: (value) => {
                if (value && value.trim()) {
                    this.addList(value.trim());
                }
            }
        });
    }

    /**
     * 显示排序选项
     */
    showSortOptions() {
        console.log('显示排序选项');

        // 先关闭已存在的排序菜单
        this.closeSortOptionsMenu();

        const sortBtn = this.todoContainer.querySelector('.sort-btn');
        if (!sortBtn) return;

        // 创建排序选项菜单
        const menu = document.createElement('div');
        menu.className = 'sort-options-menu';
        menu.style.cssText = `
            position: fixed;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            z-index: 10050;
            min-width: 200px;
            padding: 8px 0;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.2s ease;
        `;

        const currentSort = this.settings.sortBy || 'quadrant';
        const sortOptions = [
            { value: 'alphabetical', label: '字母顺序', icon: 'fas fa-sort-alpha-down', desc: '按任务标题A-Z排序' },
            { value: 'dueDate', label: '到期日期', icon: 'fas fa-calendar-alt', desc: '按截止时间先后排序' },
            { value: 'created', label: '创建日期', icon: 'fas fa-clock', desc: '按创建时间排序' },
            { value: 'quadrant', label: '重要性', icon: 'fas fa-star', desc: '按四象限优先级排序' }
        ];

        menu.innerHTML = `
            <div class="sort-menu-header">
                <i class="fas fa-sort"></i>
                <span>排序方式</span>
            </div>
            ${sortOptions.map(option => `
                <div class="sort-option ${currentSort === option.value ? 'selected' : ''}"
                     data-sort="${option.value}">
                    <div class="sort-option-main">
                        <i class="${option.icon}"></i>
                        <span class="sort-option-label">${option.label}</span>
                        ${currentSort === option.value ? '<i class="fas fa-check sort-check"></i>' : ''}
                    </div>
                    <div class="sort-option-desc">${option.desc}</div>
                </div>
            `).join('')}
        `;

        // 定位菜单
        const buttonRect = sortBtn.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let left = buttonRect.right + 4;
        let top = buttonRect.top;

        // 边界检测
        if (left + 200 > viewportWidth) {
            left = buttonRect.left - 200 - 4;
        }
        if (top + 200 > viewportHeight) {
            top = buttonRect.bottom - 200;
        }

        left = Math.max(4, Math.min(left, viewportWidth - 200 - 4));
        top = Math.max(4, Math.min(top, viewportHeight - 200 - 4));

        menu.style.left = left + 'px';
        menu.style.top = top + 'px';

        document.body.appendChild(menu);

        // 绑定事件
        this.bindSortOptionsMenuEvents(menu);

        // 显示动画
        requestAnimationFrame(() => {
            menu.style.opacity = '1';
            menu.style.transform = 'translateY(0)';
        });

        // 外部点击关闭
        const handleOutsideClick = (event) => {
            if (!menu.contains(event.target) && !sortBtn.contains(event.target)) {
                this.closeSortOptionsMenu();
                document.removeEventListener('click', handleOutsideClick);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', handleOutsideClick);
        }, 100);
    }

    /**
     * 绑定排序选项菜单事件
     */
    bindSortOptionsMenuEvents(menu) {
        const sortOptions = menu.querySelectorAll('.sort-option');

        sortOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const sortValue = option.dataset.sort;
                this.changeSortBy(sortValue);
                this.closeSortOptionsMenu();
            });

            // 悬停效果
            option.addEventListener('mouseenter', () => {
                if (!option.classList.contains('selected')) {
                    option.style.backgroundColor = '#f8f9fa';
                }
            });

            option.addEventListener('mouseleave', () => {
                if (!option.classList.contains('selected')) {
                    option.style.backgroundColor = '';
                }
            });
        });
    }

    /**
     * 关闭排序选项菜单
     */
    closeSortOptionsMenu() {
        const existingMenu = document.querySelector('.sort-options-menu');
        if (existingMenu) {
            existingMenu.style.opacity = '0';
            existingMenu.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (existingMenu.parentNode) {
                    existingMenu.parentNode.removeChild(existingMenu);
                }
            }, 200);
        }
    }

    /**
     * 更改排序方式
     */
    changeSortBy(sortBy) {
        console.log('更改排序方式:', sortBy);

        // 更新设置
        this.settings.sortBy = sortBy;
        this.saveData();

        // 更新UI显示当前排序
        this.updateSortButtonDisplay(sortBy);

        // 重新渲染任务列表
        this.refreshCurrentView();

        // 排序更改无需通知，直接应用即可
        console.log(`排序已更改为: ${this.getSortDisplayName(sortBy)}`);
    }

    /**
     * 获取排序方式的显示名称
     */
    getSortDisplayName(sortBy) {
        const names = {
            'alphabetical': '字母顺序',
            'dueDate': '到期日期',
            'created': '创建日期',
            'quadrant': '重要性'
        };
        return names[sortBy] || '默认排序';
    }

    /**
     * 更新排序按钮显示
     */
    updateSortButtonDisplay(sortBy) {
        const sortBtn = this.todoContainer.querySelector('.sort-btn');
        if (!sortBtn) return;

        const icons = {
            'alphabetical': 'fas fa-sort-alpha-down',
            'dueDate': 'fas fa-calendar-alt',
            'created': 'fas fa-clock',
            'quadrant': 'fas fa-star'
        };

        const iconElement = sortBtn.querySelector('i');
        if (iconElement) {
            iconElement.className = icons[sortBy] || 'fas fa-sort';
        }

        // 更新提示文本
        const displayName = this.getSortDisplayName(sortBy);
        sortBtn.title = `排序: ${displayName}`;
    }

    /**
     * 刷新当前视图
     */
    refreshCurrentView() {
        const tasksContainer = this.todoContainer.querySelector('.tasks-container');
        if (tasksContainer) {
            const currentView = this.getCurrentView();
            const currentListId = this.getCurrentListId();
            tasksContainer.innerHTML = this.renderTasks(currentView, currentListId);
        }
    }

    /**
     * 获取当前视图
     */
    getCurrentView() {
        const activeNavItem = this.todoContainer.querySelector('.nav-item.active');
        return activeNavItem ? activeNavItem.dataset.view : 'today';
    }

    /**
     * 获取当前列表ID
     */
    getCurrentListId() {
        const activeListItem = this.todoContainer.querySelector('.list-item.active');
        return activeListItem ? activeListItem.dataset.listId : null;
    }

    /**
     * 获取提醒状态
     */
    getReminderStatus(task) {
        if (!task.reminderDate) return '';

        const now = new Date();
        const reminderTime = new Date(task.reminderDate);

        if (now > reminderTime) {
            return 'overdue'; // 提醒时间已过
        } else {
            return 'pending'; // 等待提醒
        }
    }

    /**
     * 显示筛选选项
     */
    showFilterOptions() {
        console.log('显示筛选选项');
        this.showNotification({
            title: '功能开发中',
            message: '筛选功能正在开发中，敬请期待！',
            type: 'info'
        });
    }

    /**
     * 显示任务更多选项
     */
    showTaskMoreOptions(taskId, buttonElement) {
        console.log('显示任务更多选项:', taskId, buttonElement);

        // 先关闭已存在的菜单
        this.closeTaskOptionsMenu();

        const task = this.getTask(taskId);
        if (!task) {
            console.error('找不到任务:', taskId);
            return;
        }

        // 验证按钮元素
        if (!buttonElement || !buttonElement.getBoundingClientRect) {
            console.error('无效的按钮元素:', buttonElement);
            return;
        }

        // 创建下拉菜单
        const menu = document.createElement('div');
        menu.className = 'task-options-menu';
        menu.dataset.taskId = taskId; // 添加任务ID用于调试

        // 改进的菜单样式
        menu.style.cssText = `
            position: fixed;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            z-index: 10050;
            min-width: 140px;
            padding: 8px 0;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.2s ease;
        `;

        menu.innerHTML = `
            <div class="menu-item archive-item" data-task-id="${taskId}" style="
                padding: 8px 16px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                transition: background-color 0.2s ease;
            ">
                <i class="fas fa-archive" style="width: 16px; color: #6c757d;"></i>
                <span>${task.archived ? '取消归档' : '归档任务'}</span>
            </div>
            <div class="menu-divider" style="
                height: 1px;
                background: #e9ecef;
                margin: 4px 0;
            "></div>
            <div class="menu-item delete-item" data-task-id="${taskId}" style="
                padding: 8px 16px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                color: #dc3545;
                transition: background-color 0.2s ease;
            ">
                <i class="fas fa-trash" style="width: 16px;"></i>
                <span>删除任务</span>
            </div>
        `;

        // 改进的定位计算
        const buttonRect = buttonElement.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 计算菜单尺寸（估算）
        const menuWidth = 140;
        const menuHeight = 80;

        // 默认位置：按钮右下方
        let left = buttonRect.right + 4;
        let top = buttonRect.top;

        // 边界检测和调整
        if (left + menuWidth > viewportWidth) {
            // 如果右侧空间不够，显示在按钮左侧
            left = buttonRect.left - menuWidth - 4;
        }

        if (top + menuHeight > viewportHeight) {
            // 如果下方空间不够，显示在按钮上方
            top = buttonRect.bottom - menuHeight;
        }

        // 确保不超出视口边界
        left = Math.max(4, Math.min(left, viewportWidth - menuWidth - 4));
        top = Math.max(4, Math.min(top, viewportHeight - menuHeight - 4));

        menu.style.left = left + 'px';
        menu.style.top = top + 'px';

        console.log('菜单定位:', { left, top, buttonRect, viewportWidth, viewportHeight });

        // 添加到body而不是容器，避免定位问题
        document.body.appendChild(menu);

        // 添加悬停效果
        const menuItems = menu.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = '#f8f9fa';
            });
            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = 'transparent';
            });
        });

        // 绑定菜单事件
        this.bindTaskOptionsMenuEvents(menu);

        // 显示动画
        requestAnimationFrame(() => {
            menu.style.opacity = '1';
            menu.style.transform = 'translateY(0)';
        });

        // 改进的外部点击处理
        const handleOutsideClick = (event) => {
            if (!menu.contains(event.target) && !buttonElement.contains(event.target)) {
                this.closeTaskOptionsMenu();
                document.removeEventListener('click', handleOutsideClick);
            }
        };

        // 延迟绑定外部点击事件，避免立即触发
        setTimeout(() => {
            document.addEventListener('click', handleOutsideClick);
        }, 100);
    }

    /**
     * 显示列表更多选项
     */
    showListMoreOptions(listId, buttonElement) {
        console.log('显示列表更多选项:', listId);

        // 先关闭已存在的菜单
        this.closeListOptionsMenu();

        const list = this.lists.find(l => l.id === listId);
        if (!list) return;

        // 创建下拉菜单
        const menu = document.createElement('div');
        menu.className = 'list-options-menu';
        menu.style.cssText = `
            position: absolute;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 140px;
            padding: 4px 0;
        `;

        menu.innerHTML = `
            <div class="menu-item edit-list-item" data-list-id="${listId}">
                <i class="fas fa-edit"></i>
                <span>重命名</span>
            </div>
            <div class="menu-divider"></div>
            <div class="menu-item delete-list-item" data-list-id="${listId}">
                <i class="fas fa-trash"></i>
                <span>删除列表</span>
            </div>
        `;

        // 定位菜单
        const buttonRect = buttonElement.getBoundingClientRect();
        const containerRect = this.todoContainer.getBoundingClientRect();

        menu.style.left = (buttonRect.left - containerRect.left - 100) + 'px';
        menu.style.top = (buttonRect.bottom - containerRect.top + 4) + 'px';

        // 添加到容器
        this.todoContainer.appendChild(menu);

        // 绑定菜单事件
        this.bindListOptionsMenuEvents(menu);

        // 点击外部关闭菜单
        setTimeout(() => {
            document.addEventListener('click', this.handleListMenuOutsideClick.bind(this), { once: true });
        }, 0);
    }

    /**
     * 绑定列表选项菜单事件
     */
    bindListOptionsMenuEvents(menu) {
        const editItem = menu.querySelector('.edit-list-item');
        const deleteItem = menu.querySelector('.delete-list-item');

        if (editItem) {
            editItem.addEventListener('click', (e) => {
                e.stopPropagation();
                const listId = editItem.dataset.listId;
                this.showEditListDialog(listId);
                this.closeListOptionsMenu();
            });
        }

        if (deleteItem) {
            deleteItem.addEventListener('click', (e) => {
                e.stopPropagation();
                const listId = deleteItem.dataset.listId;
                this.confirmDeleteList(listId);
                this.closeListOptionsMenu();
            });
        }
    }

    /**
     * 处理列表菜单外部点击
     */
    handleListMenuOutsideClick(event) {
        const menu = this.todoContainer.querySelector('.list-options-menu');
        if (menu && !menu.contains(event.target)) {
            this.closeListOptionsMenu();
        }
    }

    /**
     * 关闭列表选项菜单
     */
    closeListOptionsMenu() {
        const existingMenu = this.todoContainer.querySelector('.list-options-menu');
        if (existingMenu) {
            existingMenu.remove();
        }
    }

    /**
     * 绑定任务选项菜单事件
     */
    bindTaskOptionsMenuEvents(menu) {
        const archiveItem = menu.querySelector('.archive-item');
        const deleteItem = menu.querySelector('.delete-item');

        if (archiveItem) {
            archiveItem.addEventListener('click', (e) => {
                e.stopPropagation();
                const taskId = archiveItem.dataset.taskId;
                this.toggleTaskArchive(taskId);
                this.closeTaskOptionsMenu();
            });
        }

        if (deleteItem) {
            deleteItem.addEventListener('click', (e) => {
                e.stopPropagation();
                const taskId = deleteItem.dataset.taskId;
                this.confirmDeleteTask(taskId);
                this.closeTaskOptionsMenu();
            });
        }
    }

    /**
     * 处理点击外部关闭菜单
     */
    handleOutsideClick(event) {
        const containerMenu = this.todoContainer.querySelector('.task-options-menu');
        const bodyMenu = document.body.querySelector('.task-options-menu');
        const menu = bodyMenu || containerMenu;

        if (menu && !menu.contains(event.target)) {
            this.closeTaskOptionsMenu();
        }
    }

    /**
     * 关闭任务选项菜单
     */
    closeTaskOptionsMenu() {
        // 从todoContainer中查找
        const containerMenu = this.todoContainer.querySelector('.task-options-menu');
        if (containerMenu) {
            containerMenu.remove();
            console.log('从容器中移除菜单');
        }

        // 从body中查找（新的菜单位置）
        const bodyMenu = document.body.querySelector('.task-options-menu');
        if (bodyMenu) {
            bodyMenu.remove();
            console.log('从body中移除菜单');
        }
    }

    /**
     * 显示确认对话框
     */
    showConfirmDialog(options) {
        const {
            title = '确认操作',
            message = '确定要执行此操作吗？',
            detail = '',
            confirmText = '确定',
            cancelText = '取消',
            type = 'default', // default, danger, warning, success
            onConfirm = () => {},
            onCancel = () => {}
        } = options;

        // 创建对话框
        const dialog = document.createElement('div');
        dialog.className = 'confirm-dialog-overlay';
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 10030;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.2s ease-out;
        `;

        const typeColors = {
            default: { primary: '#007bff', bg: '#e3f2fd' },
            danger: { primary: '#dc3545', bg: '#ffebee' },
            warning: { primary: '#ffc107', bg: '#fff8e1' },
            success: { primary: '#28a745', bg: '#e8f5e8' }
        };

        const colors = typeColors[type] || typeColors.default;

        dialog.innerHTML = `
            <div class="confirm-dialog" style="
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                width: 90vw;
                max-width: 400px;
                overflow: hidden;
                animation: slideIn 0.3s ease-out;
            ">
                <div class="dialog-header" style="
                    background: ${colors.bg};
                    padding: 20px 24px 16px;
                    border-bottom: 1px solid #e9ecef;
                ">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div class="dialog-icon" style="
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: ${colors.primary};
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 18px;
                        ">
                            <i class="fas ${this.getDialogIcon(type)}"></i>
                        </div>
                        <div>
                            <h3 style="
                                margin: 0;
                                font-size: 18px;
                                font-weight: 600;
                                color: #333;
                            ">${title}</h3>
                        </div>
                    </div>
                </div>

                <div class="dialog-body" style="
                    padding: 24px;
                ">
                    <p style="
                        margin: 0 0 8px 0;
                        font-size: 16px;
                        color: #333;
                        line-height: 1.5;
                    ">${message}</p>
                    ${detail ? `
                        <p style="
                            margin: 8px 0 0 0;
                            font-size: 14px;
                            color: #6c757d;
                            line-height: 1.4;
                        ">${detail}</p>
                    ` : ''}
                </div>

                <div class="dialog-footer" style="
                    padding: 16px 24px 24px;
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                ">
                    <button class="dialog-cancel-btn" style="
                        padding: 10px 20px;
                        border: 1px solid #dee2e6;
                        background: white;
                        color: #6c757d;
                        border-radius: 6px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    ">${cancelText}</button>
                    <button class="dialog-confirm-btn" style="
                        padding: 10px 20px;
                        border: none;
                        background: ${colors.primary};
                        color: white;
                        border-radius: 6px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    ">${confirmText}</button>
                </div>
            </div>
        `;

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateY(-20px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
            .dialog-cancel-btn:hover {
                background: #f8f9fa !important;
                border-color: #adb5bd !important;
            }
            .dialog-confirm-btn:hover {
                opacity: 0.9 !important;
                transform: translateY(-1px) !important;
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(dialog);

        // 绑定事件
        const cancelBtn = dialog.querySelector('.dialog-cancel-btn');
        const confirmBtn = dialog.querySelector('.dialog-confirm-btn');

        const closeDialog = () => {
            dialog.style.animation = 'fadeOut 0.2s ease-out';
            setTimeout(() => {
                if (dialog.parentNode) {
                    document.body.removeChild(dialog);
                }
                if (style.parentNode) {
                    document.head.removeChild(style);
                }
            }, 200);
        };

        cancelBtn.addEventListener('click', () => {
            closeDialog();
            onCancel();
        });

        confirmBtn.addEventListener('click', () => {
            closeDialog();
            onConfirm();
        });

        // 点击背景关闭
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                closeDialog();
                onCancel();
            }
        });

        // ESC 键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                closeDialog();
                onCancel();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);
    }

    /**
     * 显示通知对话框
     */
    showNotification(options) {
        const {
            title = '提示',
            message = '',
            type = 'info', // info, success, warning, error
            duration = 0, // 0 表示需要手动关闭
            showCloseButton = true,
            isHtml = false // 是否支持HTML格式
        } = options;

        const typeColors = {
            info: { primary: '#17a2b8', bg: '#d1ecf1', icon: 'fa-info-circle' },
            success: { primary: '#28a745', bg: '#d4edda', icon: 'fa-check-circle' },
            warning: { primary: '#ffc107', bg: '#fff3cd', icon: 'fa-exclamation-triangle' },
            error: { primary: '#dc3545', bg: '#f8d7da', icon: 'fa-exclamation-circle' }
        };

        const colors = typeColors[type] || typeColors.info;

        const notification = document.createElement('div');
        notification.className = 'notification-overlay';
        notification.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(2px);
            z-index: 10025;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.2s ease-out;
        `;

        notification.innerHTML = `
            <div class="notification-dialog" style="
                background: white;
                border-radius: 12px;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
                width: 90vw;
                max-width: 360px;
                overflow: hidden;
                animation: slideIn 0.3s ease-out;
            ">
                <div class="notification-header" style="
                    background: ${colors.bg};
                    padding: 20px 24px;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                ">
                    <div class="notification-icon" style="
                        width: 32px;
                        height: 32px;
                        border-radius: 50%;
                        background: ${colors.primary};
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 14px;
                    ">
                        <i class="fas ${colors.icon}"></i>
                    </div>
                    <h4 style="
                        margin: 0;
                        font-size: 16px;
                        font-weight: 600;
                        color: #333;
                        flex: 1;
                    ">${title}</h4>
                    ${showCloseButton ? `
                        <button class="notification-close" style="
                            background: none;
                            border: none;
                            color: #6c757d;
                            font-size: 18px;
                            cursor: pointer;
                            padding: 4px;
                            border-radius: 4px;
                            transition: background-color 0.2s ease;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : ''}
                </div>

                <div class="notification-body" style="
                    padding: 20px 24px;
                ">
                    ${isHtml ? `
                        <div style="
                            font-size: 14px;
                            color: #555;
                            line-height: 1.5;
                        ">${message}</div>
                    ` : `
                        <p style="
                            margin: 0;
                            font-size: 14px;
                            color: #555;
                            line-height: 1.5;
                        ">${message}</p>
                    `}
                </div>

                ${showCloseButton ? `
                    <div class="notification-footer" style="
                        padding: 0 24px 20px;
                        text-align: right;
                    ">
                        <button class="notification-ok-btn" style="
                            padding: 8px 16px;
                            border: none;
                            background: ${colors.primary};
                            color: white;
                            border-radius: 6px;
                            font-size: 14px;
                            font-weight: 500;
                            cursor: pointer;
                            transition: all 0.2s ease;
                        ">确定</button>
                    </div>
                ` : ''}
            </div>
        `;

        document.body.appendChild(notification);

        const closeNotification = () => {
            notification.style.animation = 'fadeOut 0.2s ease-out';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 200);
        };

        // 绑定关闭事件
        if (showCloseButton) {
            const closeBtn = notification.querySelector('.notification-close');
            const okBtn = notification.querySelector('.notification-ok-btn');

            if (closeBtn) {
                closeBtn.addEventListener('click', closeNotification);
            }
            if (okBtn) {
                okBtn.addEventListener('click', closeNotification);
            }
        }

        // 点击背景关闭
        notification.addEventListener('click', (e) => {
            if (e.target === notification) {
                closeNotification();
            }
        });

        // 自动关闭
        if (duration > 0) {
            setTimeout(closeNotification, duration);
        }

        return { close: closeNotification };
    }

    /**
     * 显示输入对话框
     */
    showInputDialog(options) {
        const {
            title = '输入信息',
            message = '请输入内容',
            placeholder = '',
            defaultValue = '',
            confirmText = '确定',
            cancelText = '取消',
            onConfirm = () => {},
            onCancel = () => {}
        } = options;

        // 创建对话框
        const dialog = document.createElement('div');
        dialog.className = 'input-dialog-overlay';
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 10030;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.2s ease-out;
        `;

        dialog.innerHTML = `
            <div class="input-dialog" style="
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                width: 90vw;
                max-width: 400px;
                overflow: hidden;
                animation: slideIn 0.3s ease-out;
            ">
                <div class="dialog-header" style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 20px 24px 16px;
                    color: white;
                ">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div class="dialog-icon" style="
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: rgba(255, 255, 255, 0.2);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 18px;
                        ">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div>
                            <h3 style="
                                margin: 0;
                                font-size: 18px;
                                font-weight: 600;
                            ">${title}</h3>
                        </div>
                    </div>
                </div>

                <div class="dialog-body" style="
                    padding: 24px;
                ">
                    <p style="
                        margin: 0 0 16px 0;
                        font-size: 14px;
                        color: #6c757d;
                        line-height: 1.5;
                    ">${message}</p>

                    <input type="text" class="dialog-input"
                           placeholder="${placeholder}"
                           value="${defaultValue}"
                           style="
                               width: 100%;
                               padding: 12px 16px;
                               border: 2px solid #e9ecef;
                               border-radius: 8px;
                               font-size: 14px;
                               transition: border-color 0.2s ease;
                               box-sizing: border-box;
                           ">
                </div>

                <div class="dialog-footer" style="
                    padding: 16px 24px 24px;
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                ">
                    <button class="dialog-cancel-btn" style="
                        padding: 10px 20px;
                        border: 1px solid #dee2e6;
                        background: white;
                        color: #6c757d;
                        border-radius: 6px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    ">${cancelText}</button>
                    <button class="dialog-confirm-btn" style="
                        padding: 10px 20px;
                        border: none;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        border-radius: 6px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    ">${confirmText}</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        const input = dialog.querySelector('.dialog-input');
        const cancelBtn = dialog.querySelector('.dialog-cancel-btn');
        const confirmBtn = dialog.querySelector('.dialog-confirm-btn');

        // 输入框样式交互
        input.addEventListener('focus', () => {
            input.style.borderColor = '#667eea';
            input.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
        });

        input.addEventListener('blur', () => {
            input.style.borderColor = '#e9ecef';
            input.style.boxShadow = 'none';
        });

        const closeDialog = () => {
            dialog.style.animation = 'fadeOut 0.2s ease-out';
            setTimeout(() => {
                if (dialog.parentNode) {
                    document.body.removeChild(dialog);
                }
            }, 200);
        };

        const handleConfirm = () => {
            const value = input.value.trim();
            closeDialog();
            onConfirm(value);
        };

        const handleCancel = () => {
            closeDialog();
            onCancel();
        };

        // 绑定事件
        cancelBtn.addEventListener('click', handleCancel);
        confirmBtn.addEventListener('click', handleConfirm);

        // 回车键确认
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleConfirm();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
            }
        });

        // 点击背景关闭
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                handleCancel();
            }
        });

        // 聚焦输入框
        setTimeout(() => {
            input.focus();
            input.select();
        }, 100);
    }

    /**
     * 获取对话框图标
     */
    getDialogIcon(type) {
        const icons = {
            default: 'fa-question-circle',
            danger: 'fa-exclamation-triangle',
            warning: 'fa-exclamation-circle',
            success: 'fa-check-circle'
        };
        return icons[type] || icons.default;
    }

    /**
     * 关闭所有对话框（保留用于其他地方调用）
     */
    closeAllDialogs() {
        console.log('关闭所有对话框');
        // 移除所有模态对话框（包括添加到 body 的）
        const modals = document.querySelectorAll('.task-modal, .modal, .dialog, .popup, .confirm-dialog-overlay, .notification-overlay, .input-dialog-overlay, .task-options-menu, .list-options-menu');
        console.log(`找到 ${modals.length} 个对话框需要关闭`);

        modals.forEach((modal, index) => {
            if (modal.parentNode) {
                console.log(`关闭对话框 ${index + 1}: ${modal.className}`);
                modal.parentNode.removeChild(modal);
            }
        });

        // 额外清理：移除可能残留的事件监听器
        document.removeEventListener('keydown', this._currentEscHandler);
        this._currentEscHandler = null;
    }

    /**
     * 处理对话框确认
     */
    handleDialogConfirm(event) {
        console.log('处理对话框确认');
        const dialog = event.target.closest('.modal, .dialog, .popup');
        if (dialog) {
            // 根据对话框类型处理确认逻辑
            const dialogType = dialog.dataset.type;
            switch (dialogType) {
                case 'add-task':
                    this.handleAddTaskConfirm(dialog);
                    break;
                case 'edit-task':
                    this.handleEditTaskConfirm(dialog);
                    break;
                case 'add-list':
                    this.handleAddListConfirm(dialog);
                    break;
                default:
                    this.closeAllDialogs();
            }
        }
    }

    /**
     * 处理添加任务确认
     */
    handleAddTaskConfirm(dialog) {
        const form = dialog.querySelector('.task-form-redesigned') || dialog.querySelector('.task-form');
        if (form) {
            const formData = new FormData(form);
            const taskData = this.extractTaskDataFromForm(formData);
            if (taskData.title.trim()) {
                this.createTask(taskData);
                this.closeAllDialogs();
            } else {
                this.showNotification({
                    title: '输入错误',
                    message: '请输入任务标题',
                    type: 'warning'
                });
            }
        }
    }

    /**
     * 处理编辑任务确认
     */
    handleEditTaskConfirm(dialog) {
        const form = dialog.querySelector('.task-form-redesigned') || dialog.querySelector('.task-form');
        const taskId = dialog.dataset.taskId;
        if (form && taskId) {
            const formData = new FormData(form);
            const taskData = this.extractTaskDataFromForm(formData);
            if (taskData.title.trim()) {
                this.updateTask(taskId, taskData);
                this.closeAllDialogs();
            } else {
                this.showNotification({
                    title: '输入错误',
                    message: '请输入任务标题',
                    type: 'warning'
                });
            }
        }
    }

    /**
     * 处理添加列表确认
     */
    handleAddListConfirm(dialog) {
        const input = dialog.querySelector('input[name="listName"]');
        if (input && input.value.trim()) {
            this.addList(input.value.trim());
            this.closeAllDialogs();
        } else {
            this.showNotification({
                title: '输入错误',
                message: '请输入列表名称',
                type: 'warning'
            });
        }
    }

    /**
     * 从表单提取任务数据（基于四象限优先级体系）
     */
    extractTaskDataFromForm(formData) {
        const quadrant = formData.get('quadrant');
        const quadrantNum = quadrant ? parseInt(quadrant) : null;

        // 使用工具方法计算重要性和紧急性
        const { important, urgent } = TodoUtils.calculateImportanceUrgency(quadrantNum);

        // 处理日期时间字段 - 统一转换为ISO字符串格式
        const dueDate = formData.get('dueDate');
        const reminderDate = formData.get('reminderDate');

        return {
            title: formData.get('title') || '',
            description: formData.get('description') || '',
            status: formData.get('status') || 'not-started',
            listId: formData.get('listId') || null,
            dueDate: dueDate ? this.convertLocalDateTimeToISO(dueDate) : null,
            reminderDate: reminderDate ? this.convertLocalDateTimeToISO(reminderDate) : null,
            // 四象限作为唯一的优先级体系
            quadrant: quadrantNum,
            important: important,
            urgent: urgent
            // 移除 priority 字段处理
        };
    }

    /**
     * 将本地日期时间字符串转换为ISO字符串
     * @param {string} localDateTime - 本地日期时间字符串 (如 "2025-08-19T14:30")
     * @returns {string} ISO字符串 (如 "2025-08-19T14:30:00.000Z")
     */
    convertLocalDateTimeToISO(localDateTime) {
        if (!localDateTime) return null;

        try {
            // 创建本地时间的Date对象
            const date = new Date(localDateTime);

            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('无效的日期时间格式:', localDateTime);
                return null;
            }

            // 转换为ISO字符串
            return date.toISOString();
        } catch (error) {
            console.error('日期时间转换失败:', localDateTime, error);
            return null;
        }
    }

    /**
     * 将ISO字符串转换为本地日期时间字符串（用于表单显示）
     * @param {string} isoString - ISO字符串 (如 "2025-08-19T14:30:00.000Z")
     * @returns {string} 本地日期时间字符串 (如 "2025-08-19T14:30")
     */
    convertISOToLocalDateTime(isoString) {
        if (!isoString) return '';

        try {
            const date = new Date(isoString);

            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('无效的ISO日期格式:', isoString);
                return '';
            }

            // 获取本地时间的年月日时分
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            // 返回 datetime-local 格式
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        } catch (error) {
            console.error('ISO日期转换失败:', isoString, error);
            return '';
        }
    }

    /**
     * 检查字符串是否为有效的ISO日期格式
     * @param {string} dateString - 日期字符串
     * @returns {boolean} 是否为有效的ISO格式
     */
    isValidISOString(dateString) {
        if (!dateString || typeof dateString !== 'string') return false;

        // ISO 8601 格式应该包含 'T' 和 'Z' 或时区信息
        const isoPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;

        if (!isoPattern.test(dateString)) return false;

        // 验证日期是否可以正确解析
        const date = new Date(dateString);
        return !isNaN(date.getTime());
    }

    /**
     * 绑定全局事件
     */
    bindEvents() {
        // 监听配置变化
        if (this.navApp) {
            this.navApp.on?.('config-updated', () => {
                this.loadSettings();
            });
        }

        // 初始化排序按钮显示
        this.initializeSortButton();
    }

    /**
     * 初始化排序按钮显示
     */
    initializeSortButton() {
        const currentSort = this.settings.sortBy || 'quadrant';
        this.updateSortButtonDisplay(currentSort);
    }
    
    /**
     * 显示 ToDo 界面
     */
    show() {
        if (this.todoContainer) {
            this.todoContainer.style.display = 'block';
            this.isVisible = true;

            // 隐藏其他内容
            const contentArea = document.querySelector('.content-area');
            if (contentArea) {
                contentArea.style.display = 'none';
            }

            // 刷新UI以确保显示最新数据
            this.refreshUI();

            // 通知提醒管理器 todo 页面已激活
            if (this.reminderManager && this.reminderManager.setTodoPageActive) {
                this.reminderManager.setTodoPageActive(true);
            }

            // 启动倒计时更新
            this.startCountdownUpdates();

            this.emit('todo-shown');
        }
    }

    /**
     * 隐藏 ToDo 界面
     */
    hide() {
        if (this.todoContainer) {
            this.todoContainer.style.display = 'none';
            this.isVisible = false;

            // 显示原内容
            const contentArea = document.querySelector('.content-area');
            if (contentArea) {
                contentArea.style.display = 'block';
            }

            // 通知提醒管理器 todo 页面已隐藏
            if (this.reminderManager && this.reminderManager.setTodoPageActive) {
                this.reminderManager.setTodoPageActive(false);
            }

            // 停止倒计时更新
            this.stopCountdownUpdates();

            this.emit('todo-hidden');
        }
    }
    
    /**
     * 切换显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
    
    // ==================== 任务管理方法 ====================
    
    /**
     * 创建新任务
     */
    createTask(taskData) {
        // 处理数据迁移：如果有旧的priority但没有quadrant，进行转换
        let quadrant = taskData.quadrant;
        if (!quadrant && taskData.priority?.level) {
            quadrant = TodoUtils.priorityToQuadrant(taskData.priority.level);
            console.log(`数据迁移: priority "${taskData.priority.level}" -> quadrant ${quadrant}`);
        }

        // 计算重要性和紧急性
        const { important, urgent } = TodoUtils.calculateImportanceUrgency(quadrant);

        // 处理状态字段，确保completed字段与status字段同步
        const status = taskData.status || 'not-started';
        const completed = (status === 'completed');

        const task = {
            id: TodoUtils.generateId(),
            title: taskData.title,
            description: taskData.description || '',
            status: status,
            completed: completed,
            archived: false,
            listId: taskData.listId || this.getDefaultListId(),
            dueDate: taskData.dueDate || null,
            reminderDate: taskData.reminderDate || null,
            // 统一的四象限优先级体系
            quadrant: quadrant,
            important: important,
            urgent: urgent,
            tags: taskData.tags || [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            completedAt: completed ? new Date().toISOString() : null,
            archivedAt: null,
            steps: [],
            memo: null,
            metadata: {
                source: 'local',
                version: 1,
                checksum: ''
            }
        };
        
        // 计算校验和
        task.metadata.checksum = TodoUtils.calculateChecksum(task);
        
        this.tasks.push(task);
        this.saveData();
        this.refreshUI();
        
        this.emit('task-created', { task });
        return task;
    }
    
    /**
     * 获取任务
     */
    getTask(taskId) {
        return this.tasks.find(task => task.id === taskId);
    }
    
    /**
     * 更新任务（支持四象限优先级体系）
     */
    updateTask(taskId, updates) {
        const task = this.getTask(taskId);
        if (!task) return false;

        // 如果更新包含quadrant，重新计算important和urgent
        if (updates.quadrant !== undefined) {
            const { important, urgent } = TodoUtils.calculateImportanceUrgency(updates.quadrant);
            updates.important = important;
            updates.urgent = urgent;

            // 移除旧的priority字段（如果存在）
            if (task.priority) {
                console.log(`任务 ${taskId}: 移除旧的priority字段，使用quadrant ${updates.quadrant}`);
                delete task.priority;
            }
        }

        // 如果更新包含status，同步completed字段
        if (updates.status !== undefined) {
            updates.completed = (updates.status === 'completed');
            updates.completedAt = updates.completed ? new Date().toISOString() : null;
        }

        Object.assign(task, updates, {
            updatedAt: new Date().toISOString()
        });

        // 重新计算校验和
        task.metadata.checksum = TodoUtils.calculateChecksum(task);

        // 如果提醒时间被更新，重置提醒状态
        if (updates.reminderDate !== undefined && this.reminderManager) {
            this.reminderManager.resetTaskReminder(taskId);
        }

        this.saveData();

        // 刷新侧边栏计数
        this.refreshListCounts();
        this.refreshViewCounts();

        // 如果更新了时间相关字段，使用专门的时间更新处理
        if (updates.dueDate !== undefined || updates.reminderDate !== undefined) {
            console.log(`检测到时间字段更新，将处理任务 ${taskId} 的时间显示更新`);
            console.log(`当前页面状态: isVisible=${this.isVisible}, todoContainer存在=${!!this.todoContainer}`);
            this.handleTaskTimeUpdate(taskId);
        } else {
            // 对于非时间字段，使用精确更新
            this.updateTaskInDOM(taskId, updates);
        }

        this.emit('task-updated', { task, updates });
        return true;
    }

    /**
     * 精确更新DOM中的任务显示
     */
    updateTaskInDOM(taskId, updates) {
        if (!this.todoContainer) {
            console.log('精确更新DOM：容器不存在');
            return;
        }

        const taskItem = this.todoContainer.querySelector(`.task-item[data-task-id="${taskId}"]`);
        if (!taskItem) {
            // 如果找不到任务项，可能是因为视图切换，进行全量刷新
            this.refreshCurrentView();
            return;
        }

        const task = this.getTask(taskId);
        if (!task) return;

        // 更新任务标题
        if (updates.title !== undefined) {
            const titleElement = taskItem.querySelector('.task-title');
            if (titleElement) {
                titleElement.textContent = task.title;
            }
        }

        // 更新任务描述
        if (updates.description !== undefined) {
            const descElement = taskItem.querySelector('.task-description');
            const taskContent = taskItem.querySelector('.task-content');

            if (task.description) {
                if (descElement) {
                    descElement.textContent = task.description;
                } else {
                    // 创建描述元素
                    const newDescElement = document.createElement('p');
                    newDescElement.className = 'task-description';
                    newDescElement.textContent = task.description;
                    const taskHeader = taskContent.querySelector('.task-header');
                    taskHeader.insertAdjacentElement('afterend', newDescElement);
                }
            } else {
                // 移除描述元素
                if (descElement) {
                    descElement.remove();
                }
            }
        }

        // 更新截止时间
        if (updates.dueDate !== undefined || updates.dueDate === true) {
            this.updateTaskDueDateInDOM(taskItem, task);
        }

        // 更新提醒时间
        if (updates.reminderDate !== undefined || updates.reminderDate === true) {
            this.updateTaskReminderInDOM(taskItem, task);
        }

        // 更新四象限优先级
        if (updates.quadrant !== undefined) {
            this.updateTaskQuadrantInDOM(taskItem, task);
        }

        // 更新任务状态
        if (updates.status !== undefined) {
            this.updateTaskStatusInDOM(taskId, task.status);
        }
    }

    /**
     * 更新任务截止时间的DOM显示
     */
    updateTaskDueDateInDOM(taskItem, task) {
        const taskMeta = taskItem.querySelector('.task-meta');
        const existingDueDateElement = taskItem.querySelector('.task-due-date');

        if (task.dueDate) {
            const urgency = TodoUtils.getUrgencyLevel(task);
            const newTimeText = TodoUtils.formatDueTime(task.dueDate);

            if (existingDueDateElement) {
                // 更新现有元素 - 使用完全重建的方式确保结构正确
                existingDueDateElement.className = `task-due-date ${urgency}`;
                existingDueDateElement.setAttribute('data-due-date', task.dueDate);
                existingDueDateElement.innerHTML = `
                    <i class="fas fa-clock"></i>
                    <span class="due-time-text">${newTimeText}</span>
                `;
                console.log(`截止时间元素已更新: ${newTimeText}`);
            } else {
                // 创建新的截止时间元素
                const dueDateElement = document.createElement('span');
                dueDateElement.className = `task-due-date ${urgency}`;
                dueDateElement.setAttribute('data-due-date', task.dueDate);
                dueDateElement.innerHTML = `
                    <i class="fas fa-clock"></i>
                    <span class="due-time-text">${newTimeText}</span>
                `;
                taskMeta.insertBefore(dueDateElement, taskMeta.firstChild);
                console.log(`截止时间元素已创建: ${newTimeText}`);
            }
        } else {
            // 移除截止时间元素
            if (existingDueDateElement) {
                existingDueDateElement.remove();
                console.log('截止时间元素已移除');
            }
        }
    }

    /**
     * 更新任务提醒时间的DOM显示
     */
    updateTaskReminderInDOM(taskItem, task) {
        const taskMeta = taskItem.querySelector('.task-meta');
        const existingReminderElement = taskItem.querySelector('.task-reminder');

        if (task.reminderDate) {
            const reminderStatus = this.getReminderStatus(task);
            const newTimeText = TodoUtils.formatReminderTime(task.reminderDate);

            if (existingReminderElement) {
                // 更新现有元素 - 使用完全重建的方式确保结构正确
                existingReminderElement.className = `task-reminder ${reminderStatus}`;
                existingReminderElement.setAttribute('data-reminder-date', task.reminderDate);
                existingReminderElement.innerHTML = `
                    <i class="fas fa-bell"></i>
                    <span class="reminder-time-text">${newTimeText}</span>
                `;
                console.log(`提醒时间元素已更新: ${newTimeText}`);
            } else {
                // 创建新的提醒时间元素
                const reminderElement = document.createElement('span');
                reminderElement.className = `task-reminder ${reminderStatus}`;
                reminderElement.setAttribute('data-reminder-date', task.reminderDate);
                reminderElement.innerHTML = `
                    <i class="fas fa-bell"></i>
                    <span class="reminder-time-text">${newTimeText}</span>
                `;

                // 插入到截止时间后面，或者taskMeta的开头
                const dueDateElement = taskMeta.querySelector('.task-due-date');
                if (dueDateElement) {
                    dueDateElement.insertAdjacentElement('afterend', reminderElement);
                } else {
                    taskMeta.insertBefore(reminderElement, taskMeta.firstChild);
                }
                console.log(`提醒时间元素已创建: ${newTimeText}`);
            }
        } else {
            // 移除提醒时间元素
            if (existingReminderElement) {
                existingReminderElement.remove();
                console.log('提醒时间元素已移除');
            }
        }
    }

    /**
     * 更新任务四象限优先级的DOM显示
     */
    updateTaskQuadrantInDOM(taskItem, task) {
        const taskBadges = taskItem.querySelector('.task-badges');
        const existingQuadrantElement = taskItem.querySelector('.task-quadrant');

        if (task.quadrant) {
            const quadrantInfo = TodoUtils.getQuadrantPriorityMap()[task.quadrant];

            if (existingQuadrantElement) {
                // 更新现有元素
                existingQuadrantElement.className = `task-quadrant quadrant-${task.quadrant}`;
                existingQuadrantElement.title = `${quadrantInfo.label} - ${quadrantInfo.action}`;
                existingQuadrantElement.innerHTML = `
                    <span class="quadrant-icon">${quadrantInfo.icon}</span>
                    <span class="quadrant-text">${quadrantInfo.label}</span>
                    <span class="quadrant-priority">${quadrantInfo.level.toUpperCase()}</span>
                `;
            } else {
                // 创建新的四象限元素
                const quadrantElement = document.createElement('div');
                quadrantElement.className = `task-quadrant quadrant-${task.quadrant}`;
                quadrantElement.title = `${quadrantInfo.label} - ${quadrantInfo.action}`;
                quadrantElement.innerHTML = `
                    <span class="quadrant-icon">${quadrantInfo.icon}</span>
                    <span class="quadrant-text">${quadrantInfo.label}</span>
                    <span class="quadrant-priority">${quadrantInfo.level.toUpperCase()}</span>
                `;
                taskBadges.appendChild(quadrantElement);
            }
        } else {
            // 移除四象限元素
            if (existingQuadrantElement) {
                existingQuadrantElement.remove();
            }
        }
    }
    
    /**
     * 删除任务
     */
    deleteTask(taskId) {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex === -1) return false;

        const task = this.tasks[taskIndex];
        this.tasks.splice(taskIndex, 1);

        this.saveData();
        this.refreshUI();

        this.emit('task-deleted', { task });
        return true;
    }

    /**
     * 强制刷新特定任务项的显示
     */
    forceRefreshTaskItem(taskId) {
        console.log(`开始强制刷新任务 ${taskId} 的显示`);

        if (!this.todoContainer) {
            console.log('容器不存在，跳过刷新');
            return;
        }

        const taskItem = this.todoContainer.querySelector(`.task-item[data-task-id="${taskId}"]`);
        if (!taskItem) {
            console.log(`未找到任务 ${taskId} 的DOM元素`);
            return;
        }

        const task = this.getTask(taskId);
        if (!task) {
            console.log(`未找到任务 ${taskId} 的数据`);
            return;
        }

        console.log(`任务 ${taskId} 当前数据:`, {
            title: task.title,
            dueDate: task.dueDate,
            reminderDate: task.reminderDate
        });

        // 获取任务在列表中的位置
        const taskList = taskItem.parentElement;
        const nextSibling = taskItem.nextElementSibling;

        // 移除旧的任务项
        taskItem.remove();
        console.log(`已移除旧的任务项 ${taskId}`);

        // 创建新的任务项HTML
        const newTaskHTML = this.renderTask(task);
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newTaskHTML;
        const newTaskItem = tempDiv.firstElementChild;

        console.log(`新任务项HTML生成完成，包含时间信息:`, {
            hasDueDate: newTaskHTML.includes('task-due-date'),
            hasReminderDate: newTaskHTML.includes('task-reminder')
        });

        // 插入新的任务项到原位置
        if (nextSibling) {
            taskList.insertBefore(newTaskItem, nextSibling);
        } else {
            taskList.appendChild(newTaskItem);
        }

        console.log(`任务 ${taskId} 已强制刷新显示完成`);

        // 验证新的时间显示
        const newDueDateElement = newTaskItem.querySelector('.task-due-date .due-time-text');
        const newReminderElement = newTaskItem.querySelector('.task-reminder .reminder-time-text');

        if (newDueDateElement) {
            console.log(`新的截止时间显示: ${newDueDateElement.textContent}`);
        }
        if (newReminderElement) {
            console.log(`新的提醒时间显示: ${newReminderElement.textContent}`);
        }

        // 额外验证：直接计算并显示当前时间
        if (task.dueDate) {
            const currentDueTimeText = TodoUtils.formatDueTime(task.dueDate);
            console.log(`当前应该显示的截止时间: ${currentDueTimeText}`);
        }
        if (task.reminderDate) {
            const currentReminderTimeText = TodoUtils.formatReminderTime(task.reminderDate);
            console.log(`当前应该显示的提醒时间: ${currentReminderTimeText}`);
        }
    }

    /**
     * 专门处理任务编辑后的时间显示更新
     */
    handleTaskTimeUpdate(taskId) {
        console.log(`处理任务 ${taskId} 的时间显示更新`);

        // 立即尝试强制刷新（现在已移除可见性检查）
        this.forceRefreshTaskItem(taskId);

        // 额外的保险措施：延迟再次检查和更新
        setTimeout(() => {
            console.log(`延迟验证任务 ${taskId} 的时间显示`);
            this.verifyAndUpdateTaskTimeDisplay(taskId);
        }, 100);
    }

    /**
     * 验证并更新任务时间显示
     */
    verifyAndUpdateTaskTimeDisplay(taskId) {
        if (!this.todoContainer) {
            console.log('验证时间显示：容器不存在');
            return;
        }

        const taskItem = this.todoContainer.querySelector(`.task-item[data-task-id="${taskId}"]`);
        if (!taskItem) {
            console.log(`验证时间显示：未找到任务 ${taskId} 的DOM元素`);
            return;
        }

        const task = this.getTask(taskId);
        if (!task) {
            console.log(`验证时间显示：未找到任务 ${taskId} 的数据`);
            return;
        }

        let needsUpdate = false;

        // 检查截止时间显示
        const dueDateElement = taskItem.querySelector('.task-due-date');
        if (task.dueDate) {
            if (!dueDateElement) {
                console.log(`截止时间元素缺失，需要更新`);
                needsUpdate = true;
            } else {
                const timeTextElement = dueDateElement.querySelector('.due-time-text');
                if (timeTextElement) {
                    const expectedText = TodoUtils.formatDueTime(task.dueDate);
                    if (timeTextElement.textContent !== expectedText) {
                        console.log(`截止时间显示不正确: 期望 "${expectedText}", 实际 "${timeTextElement.textContent}"`);
                        needsUpdate = true;
                    }
                }
            }
        } else if (dueDateElement) {
            console.log(`任务无截止时间但存在截止时间元素，需要移除`);
            needsUpdate = true;
        }

        // 检查提醒时间显示
        const reminderElement = taskItem.querySelector('.task-reminder');
        if (task.reminderDate) {
            if (!reminderElement) {
                console.log(`提醒时间元素缺失，需要更新`);
                needsUpdate = true;
            } else {
                const timeTextElement = reminderElement.querySelector('.reminder-time-text');
                if (timeTextElement) {
                    const expectedText = TodoUtils.formatReminderTime(task.reminderDate);
                    if (timeTextElement.textContent !== expectedText) {
                        console.log(`提醒时间显示不正确: 期望 "${expectedText}", 实际 "${timeTextElement.textContent}"`);
                        needsUpdate = true;
                    }
                }
            }
        } else if (reminderElement) {
            console.log(`任务无提醒时间但存在提醒时间元素，需要移除`);
            needsUpdate = true;
        }

        if (needsUpdate) {
            console.log(`时间显示验证失败，执行最终强制刷新`);
            this.forceRefreshTaskItem(taskId);
        } else {
            console.log(`时间显示验证通过，无需更新`);
        }
    }

    /**
     * 确认删除任务
     */
    confirmDeleteTask(taskId) {
        const task = this.getTask(taskId);
        if (!task) return;

        this.showConfirmDialog({
            title: '删除任务',
            message: `确定要删除任务 "${task.title}" 吗？`,
            detail: '此操作不可撤销，请谨慎操作。',
            confirmText: '删除',
            cancelText: '取消',
            type: 'danger',
            onConfirm: () => {
                this.deleteTask(taskId);
            }
        });
    }

    /**
     * 切换任务归档状态
     */
    toggleTaskArchive(taskId) {
        const task = this.getTask(taskId);
        if (!task) return false;

        const wasArchived = task.archived;
        task.archived = !task.archived;
        task.archivedAt = task.archived ? new Date().toISOString() : null;
        task.updatedAt = new Date().toISOString();

        // 如果归档任务，自动标记为完成
        if (task.archived && !task.completed) {
            task.completed = true;
            task.status = 'completed';
            task.completedAt = new Date().toISOString();
        }

        // 保存数据
        this.saveData();

        // 精确更新DOM，无论todo面板是否可见
        this.updateTaskArchiveInDOM(taskId, task.archived);

        // 更新计数（无论面板是否可见都需要更新）
        this.refreshListCounts();
        this.refreshViewCounts();

        const action = task.archived ? 'archived' : 'unarchived';
        console.log(`任务 ${task.title} 已${task.archived ? '归档' : '取消归档'}`);
        this.emit('task-' + action, { task });

        return true;
    }

    /**
     * 精确更新DOM中的任务归档状态
     */
    updateTaskArchiveInDOM(taskId, isArchived) {
        // 无论todo面板是否可见，都尝试更新DOM
        if (!this.todoContainer) {
            console.log('Todo容器不存在，跳过DOM更新');
            return;
        }

        const taskItem = this.todoContainer.querySelector(`.task-item[data-task-id="${taskId}"]`);
        if (!taskItem) {
            console.log(`任务项 ${taskId} 不在当前视图中，跳过DOM更新`);
            return;
        }

        const currentView = this.getCurrentView();

        if (isArchived) {
            // 任务被归档
            if (currentView === 'archived') {
                // 如果在归档视图中，需要重新渲染以显示归档的任务
                this.refreshCurrentView();
            } else {
                // 在其他视图中，任务应该消失
                this.animateTaskRemoval(taskItem, '任务已归档');
            }
        } else {
            // 任务被取消归档
            if (currentView === 'archived') {
                // 在归档视图中，任务应该消失
                this.animateTaskRemoval(taskItem, '任务已取消归档');
            } else {
                // 在其他视图中，需要重新渲染以显示取消归档的任务
                this.refreshCurrentView();
            }
        }
    }

    /**
     * 动画移除任务项
     */
    animateTaskRemoval(taskItem, message) {
        // 记录原始高度
        const originalHeight = taskItem.offsetHeight;
        taskItem.style.maxHeight = originalHeight + 'px';

        // 添加归档动画效果
        taskItem.classList.add('archiving');

        // 显示操作提示
        if (message) {
            this.showNotification({
                title: '操作成功',
                message: message,
                type: 'success',
                duration: 2000
            });
        }

        // 动画完成后收缩高度
        setTimeout(() => {
            if (taskItem.parentNode) {
                taskItem.classList.remove('archiving');
                taskItem.classList.add('collapsing');

                // 高度动画完成后移除元素
                setTimeout(() => {
                    if (taskItem.parentNode) {
                        taskItem.remove();
                    }
                }, 200);
            }
        }, 400);
    }

    /**
     * 添加新列表
     */
    addList(name) {
        const newList = {
            id: this.generateId(),
            name: name,
            icon: 'fas fa-list',
            color: '#0078d4',
            createdAt: new Date().toISOString(),
            taskCount: 0
        };

        this.lists.push(newList);
        this.saveData();
        this.refreshUI();

        console.log('添加新列表:', newList);
        this.emit('list-added', { list: newList });
        return newList;
    }

    /**
     * 删除列表
     */
    deleteList(listId) {
        const listIndex = this.lists.findIndex(list => list.id === listId);
        if (listIndex === -1) return false;

        const list = this.lists[listIndex];

        // 检查是否正在查看被删除的列表
        const currentViewTitle = this.todoContainer.querySelector('.view-title');
        const isViewingDeletedList = currentViewTitle && currentViewTitle.textContent === list.name;

        this.lists.splice(listIndex, 1);

        // 删除该列表下的所有任务
        const deletedTasksCount = this.tasks.filter(task => task.listId === listId).length;
        this.tasks = this.tasks.filter(task => task.listId !== listId);

        this.saveData();
        this.refreshUI();

        // 如果正在查看被删除的列表，切换到默认视图
        if (isViewingDeletedList) {
            this.switchView('today');
        }

        console.log(`列表 "${list.name}" 已删除，同时删除了 ${deletedTasksCount} 个任务`);
        this.emit('list-deleted', { list, deletedTasksCount });

        // 显示删除成功提示
        this.showNotification({
            title: '删除成功',
            message: `列表 "${list.name}" 已删除${deletedTasksCount > 0 ? `，同时删除了 ${deletedTasksCount} 个任务` : ''}`,
            type: 'success',
            duration: 3000
        });

        return true;
    }

    /**
     * 显示编辑列表对话框
     */
    showEditListDialog(listId) {
        const list = this.lists.find(l => l.id === listId);
        if (!list) return;

        this.showInputDialog({
            title: '重命名列表',
            message: '请输入新的列表名称',
            placeholder: '输入列表名称...',
            defaultValue: list.name,
            confirmText: '保存',
            cancelText: '取消',
            onConfirm: (value) => {
                if (value && value.trim() && value.trim() !== list.name) {
                    this.updateListName(listId, value.trim());
                }
            }
        });
    }

    /**
     * 确认删除列表
     */
    confirmDeleteList(listId) {
        const list = this.lists.find(l => l.id === listId);
        if (!list) return;

        const taskCount = this.getListTasksCount(listId);
        const message = taskCount > 0
            ? `确定要删除列表 "${list.name}" 吗？\n该列表包含 ${taskCount} 个任务，删除后任务也会被删除。`
            : `确定要删除列表 "${list.name}" 吗？`;

        this.showConfirmDialog({
            title: '删除列表',
            message: message,
            detail: '此操作不可撤销，请谨慎操作。',
            confirmText: '删除',
            cancelText: '取消',
            type: 'danger',
            onConfirm: () => {
                this.deleteList(listId);
            }
        });
    }

    /**
     * 更新列表名称
     */
    updateListName(listId, newName) {
        const list = this.lists.find(l => l.id === listId);
        if (!list) return false;

        const oldName = list.name;
        list.name = newName;
        list.updatedAt = new Date().toISOString();

        this.saveData();
        this.refreshUI();

        console.log(`列表名称已更新: ${oldName} → ${newName}`);
        this.emit('list-updated', { list, oldName, newName });

        // 如果当前正在查看这个列表，更新标题
        const viewTitle = this.todoContainer.querySelector('.view-title');
        if (viewTitle && viewTitle.textContent === oldName) {
            viewTitle.textContent = newName;
        }

        return true;
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return 'todo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 循环切换任务状态：未开始 -> 进行中 -> 已完成 -> 未开始
     */
    cycleTaskStatus(taskId) {
        // 防抖处理，避免快速连续点击
        if (this._statusChangeDebounce.has(taskId)) {
            return false;
        }

        this._statusChangeDebounce.set(taskId, true);
        setTimeout(() => {
            this._statusChangeDebounce.delete(taskId);
        }, 300);

        const task = this.getTask(taskId);
        if (!task) return false;

        const currentStatus = task.status || 'not-started';
        let newStatus;

        // 状态循环：not-started -> in-progress -> completed -> not-started
        switch (currentStatus) {
            case 'not-started':
                newStatus = 'in-progress';
                break;
            case 'in-progress':
                newStatus = 'completed';
                break;
            case 'completed':
                newStatus = 'not-started';
                break;
            default:
                newStatus = 'not-started';
        }

        // 更新任务数据
        task.status = newStatus;
        task.completed = (newStatus === 'completed');
        task.completedAt = task.completed ? new Date().toISOString() : null;
        task.updatedAt = new Date().toISOString();

        // 保存数据
        this.saveData();

        // 精确更新DOM，避免全量刷新
        this.updateTaskStatusInDOM(taskId, newStatus, true);

        // 更新侧边栏计数
        this.refreshListCounts();
        this.refreshViewCounts();

        // 检查任务是否应该在当前视图中显示
        this.checkTaskVisibilityInCurrentView(taskId, newStatus);

        // 播放状态切换音效（可选）
        this.playStatusChangeSound(newStatus);

        this.emit('task-status-changed', { task, oldStatus: currentStatus, newStatus });
        return true;
    }

    /**
     * 精确更新DOM中的任务状态显示
     */
    updateTaskStatusInDOM(taskId, newStatus, withAnimation = false) {
        if (!this.todoContainer) return;

        const taskItem = this.todoContainer.querySelector(`.task-item[data-task-id="${taskId}"]`);
        if (!taskItem) return;

        // 更新任务项的data-status属性
        taskItem.setAttribute('data-status', newStatus);

        // 更新状态选择器
        const statusSelector = taskItem.querySelector('.task-status-selector');
        if (statusSelector) {
            // 添加动画效果
            if (withAnimation) {
                statusSelector.classList.add('status-changing');
                setTimeout(() => {
                    statusSelector.classList.remove('status-changing');
                }, 300);
            }

            statusSelector.setAttribute('data-status', newStatus);
            statusSelector.setAttribute('aria-label', this.getStatusAriaLabel(newStatus));

            // 更新提示文本
            const statusIndicator = statusSelector.querySelector('.status-indicator');
            if (statusIndicator) {
                statusIndicator.setAttribute('title', this.getStatusTooltip(newStatus));
            }
        }

        // 更新任务标题的样式
        const taskTitle = taskItem.querySelector('.task-title');
        if (taskTitle) {
            // 清除所有内联样式，让CSS类来控制样式
            taskTitle.style.textDecoration = '';
            taskTitle.style.color = '';
            taskTitle.style.fontWeight = '';

            // 根据状态应用相应的CSS类
            taskTitle.classList.remove('status-not-started', 'status-in-progress', 'status-completed');
            taskTitle.classList.add(`status-${newStatus}`);
        }

        // 更新任务项的整体透明度（通过CSS类控制）
        taskItem.classList.remove('status-not-started', 'status-in-progress', 'status-completed');
        taskItem.classList.add(`status-${newStatus}`);

        console.log(`任务 ${taskId} 状态已更新为: ${newStatus}`);
    }

    /**
     * 检查任务在当前视图中的可见性
     */
    checkTaskVisibilityInCurrentView(taskId, newStatus) {
        const currentView = this.getCurrentView();
        const currentListId = this.getCurrentListId();
        const task = this.getTask(taskId);

        if (!task) return;

        // 获取当前视图应该显示的任务
        const visibleTasks = this.getTasksByView(currentView, currentListId);
        const shouldBeVisible = visibleTasks.some(t => t.id === taskId);

        const taskItem = this.todoContainer.querySelector(`.task-item[data-task-id="${taskId}"]`);
        const isCurrentlyVisible = !!taskItem;

        if (isCurrentlyVisible && !shouldBeVisible) {
            // 任务应该隐藏，添加淡出动画后移除
            if (taskItem) {
                taskItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                taskItem.style.opacity = '0';
                taskItem.style.transform = 'translateX(-20px)';

                setTimeout(() => {
                    if (taskItem.parentNode) {
                        taskItem.remove();
                    }
                }, 300);
            }
        } else if (!isCurrentlyVisible && shouldBeVisible) {
            // 任务应该显示，重新渲染当前视图
            this.refreshCurrentView();
        }
    }

    /**
     * 切换任务完成状态（保持向后兼容）
     */
    toggleTaskCompletion(taskId) {
        const task = this.getTask(taskId);
        if (!task) return false;

        // 如果当前是已完成，切换到未开始；否则切换到已完成
        const newStatus = task.completed ? 'not-started' : 'completed';

        task.status = newStatus;
        task.completed = (newStatus === 'completed');
        task.completedAt = task.completed ? new Date().toISOString() : null;
        task.updatedAt = new Date().toISOString();

        this.saveData();
        this.updateTaskStatusInDOM(taskId, newStatus);

        this.emit('task-toggled', { task });
        return true;
    }
    
    // ==================== 数据获取方法 ====================
    
    getActiveTasksCount() {
        return this.tasks.filter(task => !task.completed && !task.archived).length;
    }
    
    getCompletedTasksCount() {
        return this.tasks.filter(task => task.completed && !task.archived).length;
    }
    
    getTodayTasksCount() {
        return this.tasks.filter(task => 
            !task.archived && TodoUtils.isToday(task.dueDate)
        ).length;
    }
    
    getImportantTasksCount() {
        return this.tasks.filter(task =>
            !task.archived && task.important
        ).length;
    }

    getUrgentTasksCount() {
        return this.tasks.filter(task =>
            !task.archived && task.urgent
        ).length;
    }
    
    getPlannedTasksCount() {
        return this.tasks.filter(task => 
            !task.archived && task.dueDate
        ).length;
    }
    
    getListTasksCount(listId) {
        return this.tasks.filter(task => 
            !task.archived && task.listId === listId
        ).length;
    }

    getArchivedTasksCount() {
        return this.tasks.filter(task => task.archived).length;
    }

    getCompletedStepsCount(task) {
        if (!task.steps) return 0;
        return task.steps.filter(step => step.completed).length;
    }
    
    getDefaultListId() {
        const defaultList = this.lists.find(list => list.isDefault);
        return defaultList ? defaultList.id : (this.lists[0]?.id || null);
    }
    
    /**
     * 获取优先级配置（基于四象限，保持向后兼容）
     */
    getPriorityConfig(priority) {
        const configs = {
            'low': { icon: 'fas fa-arrow-down', color: '#6c757d' },
            'medium': { icon: 'fas fa-minus', color: '#ffc107' },
            'high': { icon: 'fas fa-arrow-up', color: '#fd7e14' },
            'urgent': { icon: 'fas fa-exclamation-triangle', color: '#dc3545' }
        };
        return configs[priority] || configs.medium;
    }
    
    getTasksByView(view, listId = null) {
        let filtered;

        // 归档视图特殊处理
        if (view === 'archived') {
            filtered = this.tasks.filter(task => task.archived);
        } else if (view === 'list' && listId) {
            // 列表视图：显示指定列表的任务
            filtered = this.tasks.filter(task => !task.archived && task.listId === listId);
        } else {
            filtered = this.tasks.filter(task => !task.archived);

            switch (view) {
                case 'today':
                    filtered = filtered.filter(task => TodoUtils.isToday(task.dueDate));
                    break;
                case 'important':
                    filtered = filtered.filter(task => task.important);
                    break;
                case 'urgent':
                    filtered = filtered.filter(task => task.urgent);
                    break;
                case 'planned':
                    filtered = filtered.filter(task => task.dueDate);
                    break;
                case 'completed':
                    filtered = filtered.filter(task => task.completed);
                    break;
                default:
                    // 'all' - 显示所有活动任务
                    break;
            }
        }

        // 默认使用四象限排序，回退到设置中的排序方式
        const sortBy = this.settings.sortBy || 'quadrant';
        return TodoUtils.sortTasks(filtered, sortBy);
    }
    
    // ==================== UI 方法 ====================
    
    switchView(view) {
        // 更新导航状态
        const navItems = this.todoContainer.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.toggle('active', item.dataset.view === view);
        });
        
        // 更新视图标题
        const viewTitle = this.todoContainer.querySelector('.view-title');
        if (viewTitle) {
            const titles = {
                'today': '今天',
                'important': '重要',
                'urgent': '紧急',
                'planned': '已计划',
                'all': '全部任务',
                'archived': '已归档'
            };
            viewTitle.textContent = titles[view] || '任务';
        }
        
        // 重新渲染任务
        const tasksContainer = this.todoContainer.querySelector('.tasks-container');
        if (tasksContainer) {
            tasksContainer.innerHTML = this.renderTasks(view);
        }
    }

    /**
     * 切换到列表视图
     */
    switchToListView(listId) {
        // 更新导航状态
        const navItems = this.todoContainer.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            const isActive = item.dataset.view === 'list' && item.dataset.listId === listId;
            item.classList.toggle('active', isActive);
        });

        // 获取列表信息
        const list = this.lists.find(l => l.id === listId);
        if (!list) return;

        // 更新视图标题
        const viewTitle = this.todoContainer.querySelector('.view-title');
        if (viewTitle) {
            viewTitle.textContent = list.name;
        }

        // 重新渲染任务
        const tasksContainer = this.todoContainer.querySelector('.tasks-container');
        if (tasksContainer) {
            tasksContainer.innerHTML = this.renderTasks('list', listId);
        }
    }

    /**
     * 刷新UI（智能刷新，保持当前视图状态）
     */
    refreshUI() {
        if (!this.isVisible || !this.todoContainer) {
            return;
        }

        // 刷新任务列表（保持当前视图）
        this.refreshCurrentView();

        // 刷新侧边栏列表计数
        this.refreshListCounts();

        // 刷新视图计数
        this.refreshViewCounts();

        // 启动倒计时更新定时器（用于定期更新所有倒计时）
        this.startCountdownUpdates();
    }

    /**
     * 启动倒计时更新定时器
     */
    startCountdownUpdates() {
        // 如果已经在运行，先停止
        this.stopCountdownUpdates();

        // 每30秒更新一次倒计时显示
        this.countdownUpdateInterval = setInterval(() => {
            this.updateCountdownDisplays();
        }, 30000);

        console.log('倒计时更新定时器已启动');
    }

    /**
     * 停止倒计时更新定时器
     */
    stopCountdownUpdates() {
        if (this.countdownUpdateInterval) {
            clearInterval(this.countdownUpdateInterval);
            this.countdownUpdateInterval = null;
            console.log('倒计时更新定时器已停止');
        }
    }

    /**
     * 更新所有任务的倒计时显示（定时器调用）
     */
    updateCountdownDisplays() {
        if (!this.isVisible || !this.todoContainer) {
            return;
        }

        const taskItems = this.todoContainer.querySelectorAll('.task-item');
        let updatedCount = 0;

        taskItems.forEach(taskItem => {
            const taskId = taskItem.dataset.taskId;
            const task = this.getTask(taskId);

            if (!task) return;

            // 更新截止时间倒计时（使用新的HTML结构）
            const dueDateElement = taskItem.querySelector('.task-due-date');
            if (dueDateElement && task.dueDate) {
                const timeTextElement = dueDateElement.querySelector('.due-time-text');
                if (timeTextElement) {
                    const newTimeText = TodoUtils.formatDueTime(task.dueDate);
                    timeTextElement.textContent = newTimeText;

                    // 更新紧急程度样式
                    const urgency = TodoUtils.getUrgencyLevel(task);
                    dueDateElement.className = `task-due-date ${urgency}`;
                    dueDateElement.setAttribute('data-due-date', task.dueDate);
                    updatedCount++;
                }
            }

            // 更新提醒时间倒计时（使用新的HTML结构）
            const reminderElement = taskItem.querySelector('.task-reminder');
            if (reminderElement && task.reminderDate) {
                const timeTextElement = reminderElement.querySelector('.reminder-time-text');
                if (timeTextElement) {
                    const newTimeText = TodoUtils.formatReminderTime(task.reminderDate);
                    timeTextElement.textContent = newTimeText;

                    // 更新提醒状态样式
                    const reminderStatus = this.getReminderStatus(task);
                    reminderElement.className = `task-reminder ${reminderStatus}`;
                    reminderElement.setAttribute('data-reminder-date', task.reminderDate);
                    updatedCount++;
                }
            }
        });

        if (updatedCount > 0) {
            console.log(`定时器更新倒计时显示，共更新 ${updatedCount} 个时间显示`);
        }
    }

    /**
     * 刷新列表计数
     */
    refreshListCounts() {
        if (!this.todoContainer) {
            console.log('Todo容器不存在，跳过列表计数更新');
            return;
        }

        const listItems = this.todoContainer.querySelectorAll('.list-item');
        listItems.forEach(item => {
            const listId = item.dataset.listId;
            const countElement = item.querySelector('.nav-count');
            if (countElement && listId) {
                countElement.textContent = this.getListTasksCount(listId);
            }
        });
    }

    /**
     * 刷新视图计数
     */
    refreshViewCounts() {
        if (!this.todoContainer) {
            console.log('Todo容器不存在，跳过视图计数更新');
            return;
        }

        const viewItems = this.todoContainer.querySelectorAll('.nav-item:not(.list-item)');
        viewItems.forEach(item => {
            const view = item.dataset.view;
            const countElement = item.querySelector('.nav-count');
            if (countElement && view) {
                countElement.textContent = this.getViewTasksCount(view);
            }
        });
    }

    /**
     * 获取视图任务数量
     */
    getViewTasksCount(view) {
        switch (view) {
            case 'today':
                return this.tasks.filter(task => !task.archived && TodoUtils.isToday(task.dueDate)).length;
            case 'important':
                return this.tasks.filter(task => !task.archived && task.important).length;
            case 'urgent':
                return this.tasks.filter(task => !task.archived && task.urgent).length;
            case 'planned':
                return this.tasks.filter(task => !task.archived && task.dueDate).length;
            case 'all':
                return this.tasks.filter(task => !task.archived).length;
            case 'archived':
                return this.tasks.filter(task => task.archived).length;
            default:
                return 0;
        }
    }
    
    showAddTaskDialog() {
        this.showTaskDialog();
    }

    showEditTaskDialog(taskId) {
        const task = this.getTask(taskId);
        if (task) {
            this.showTaskDialog(task);
        }
    }

    /**
     * 获取四象限图标
     */
    getQuadrantIcon(quadrant) {
        const icons = {
            1: '🔥', // 重要紧急
            2: '⭐', // 重要不紧急
            3: '⚡', // 不重要紧急
            4: '📝'  // 不重要不紧急
        };
        return icons[quadrant] || '';
    }

    /**
     * 获取四象限文本
     */
    getQuadrantText(quadrant) {
        const texts = {
            1: '重要紧急',
            2: '重要不紧急',
            3: '不重要紧急',
            4: '不重要不紧急'
        };
        return texts[quadrant] || '';
    }

    /**
     * 获取四象限选中状态的CSS类
     */
    getQuadrantClass(task, quadrant) {
        if (!task || !task.quadrant) return '';
        return task.quadrant === quadrant ? 'selected' : '';
    }

    /**
     * 获取状态提示文本
     */
    getStatusTooltip(status) {
        const statusMap = {
            'not-started': '未开始 - 点击开始任务',
            'in-progress': '进行中 - 点击标记完成',
            'completed': '已完成 - 点击重新开始'
        };
        return statusMap[status] || '点击切换状态';
    }

    /**
     * 获取状态的无障碍标签
     */
    getStatusAriaLabel(status) {
        const statusMap = {
            'not-started': '任务状态：未开始，按空格键或回车键开始任务',
            'in-progress': '任务状态：进行中，按空格键或回车键标记完成',
            'completed': '任务状态：已完成，按空格键或回车键重新开始'
        };
        return statusMap[status] || '任务状态切换按钮';
    }

    /**
     * 播放状态切换音效
     */
    playStatusChangeSound(status) {
        // 检查用户是否启用了音效
        if (!this.settings.enableSounds) return;

        try {
            // 使用Web Audio API播放简单的音效
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // 根据状态设置不同的音调
            const frequencies = {
                'not-started': 440,    // A4
                'in-progress': 523,    // C5
                'completed': 659       // E5
            };

            oscillator.frequency.setValueAtTime(frequencies[status] || 440, audioContext.currentTime);
            oscillator.type = 'sine';

            // 设置音量和持续时间
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        } catch (error) {
            // 静默处理音效播放错误
            console.debug('音效播放失败:', error);
        }
    }



    /**
     * 显示任务编辑对话框
     */
    showTaskDialog(task = null) {
        // 首先清理所有已存在的对话框，防止叠加
        this.closeAllDialogs();

        const isEdit = !!task;
        const modal = document.createElement('div');
        modal.className = 'task-modal';
        modal.dataset.type = isEdit ? 'edit-task' : 'add-task';
        if (isEdit) {
            modal.dataset.taskId = task.id;
        }
        // 使用内联样式确保正确的层级
        this._dialogZIndex += 10; // 每个新对话框层级递增
        modal.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            z-index: ${this._dialogZIndex} !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        `;
        modal.innerHTML = `
            <div class="modal-backdrop" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); backdrop-filter: blur(4px); z-index: 1;"></div>
            <div class="modal-content" style="position: relative; z-index: 2; background: white; border-radius: 12px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3); width: 90vw; max-width: 650px; max-height: 85vh; display: flex; flex-direction: column; overflow: hidden;">
                <div class="modal-header">
                    <h3>${isEdit ? '编辑任务' : '新建任务'}</h3>
                    <button class="modal-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="task-form-redesigned">
                        <!-- 基本信息区域 -->
                        <div class="form-section">
                            <div class="section-header">
                                <i class="fas fa-edit"></i>
                                <span>基本信息</span>
                            </div>
                            <div class="form-group">
                                <label for="taskTitle">任务标题 *</label>
                                <input type="text" id="taskTitle" name="title" required
                                       value="${task ? task.title : ''}"
                                       placeholder="输入任务标题...">
                            </div>
                            <div class="form-group">
                                <label for="taskDescription">任务描述</label>
                                <textarea id="taskDescription" name="description"
                                          placeholder="输入任务描述...">${task ? task.description || '' : ''}</textarea>
                            </div>
                        </div>

                        <!-- 分类和优先级区域 -->
                        <div class="form-section">
                            <div class="section-header">
                                <i class="fas fa-layer-group"></i>
                                <span>分类与优先级</span>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="taskList">所属列表</label>
                                    <select id="taskList" name="listId">
                                        ${this.lists.map(list => `
                                            <option value="${list.id}" ${task && task.listId === list.id ? 'selected' : ''}>
                                                ${list.name}
                                            </option>
                                        `).join('')}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="taskStatus">任务状态</label>
                                    <select id="taskStatus" name="status" class="status-select">
                                        <option value="not-started" ${!task || task.status === 'not-started' ? 'selected' : ''}>⭕ 未开始</option>
                                        <option value="in-progress" ${task?.status === 'in-progress' ? 'selected' : ''}>🔄 进行中</option>
                                        <option value="completed" ${task?.status === 'completed' ? 'selected' : ''}>✅ 已完成</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="taskPriority">优先级</label>
                                    <select id="taskPriority" name="quadrant" class="priority-select-compact">
                                        <option value="">选择优先级</option>
                                        <option value="1" ${task?.quadrant === 1 ? 'selected' : ''}>🔥 重要紧急</option>
                                        <option value="2" ${task?.quadrant === 2 ? 'selected' : ''}>⭐ 重要不紧急</option>
                                        <option value="3" ${task?.quadrant === 3 ? 'selected' : ''}>⚡ 不重要紧急</option>
                                        <option value="4" ${task?.quadrant === 4 ? 'selected' : ''}>📝 不重要不紧急</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 时间设置区域 -->
                        <div class="form-section">
                            <div class="section-header">
                                <i class="fas fa-clock"></i>
                                <span>时间设置</span>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="taskDueDate">截止日期</label>
                                    <input type="datetime-local" id="taskDueDate" name="dueDate"
                                           value="${task && task.dueDate ? this.convertISOToLocalDateTime(task.dueDate) : ''}">
                                </div>
                                <div class="form-group">
                                    <label for="taskReminder">提醒时间</label>
                                    <input type="datetime-local" id="taskReminder" name="reminderDate"
                                           value="${task && task.reminderDate ? this.convertISOToLocalDateTime(task.reminderDate) : ''}">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary cancel-btn">取消</button>
                    <button type="button" class="btn-primary save-btn">${isEdit ? '保存' : '创建'}</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        console.log(`${isEdit ? '编辑' : '新建'}任务对话框已创建，z-index: ${this._dialogZIndex}`);

        // 绑定事件
        const closeBtn = modal.querySelector('.modal-close-btn');
        const cancelBtn = modal.querySelector('.cancel-btn');
        const saveBtn = modal.querySelector('.save-btn');
        const backdrop = modal.querySelector('.modal-backdrop');
        const modalContent = modal.querySelector('.modal-content');

        // 防止模态框内容的点击事件冒泡到 backdrop
        modalContent.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // 优先级选择器事件（简化版）
        const prioritySelect = modal.querySelector('#taskPriority');
        if (prioritySelect) {
            prioritySelect.addEventListener('change', (e) => {
                const quadrant = e.target.value;
                console.log('选择优先级:', quadrant);
            });
        }

        let isClosing = false; // 防止重复关闭

        const closeModal = () => {
            if (isClosing) return;
            isClosing = true;

            console.log(`关闭${isEdit ? '编辑' : '新建'}任务对话框`);
            if (modal && modal.parentNode) {
                document.body.removeChild(modal);
                console.log('对话框已从DOM中移除');
            }

            // 清理ESC处理器
            if (this._currentEscHandler) {
                document.removeEventListener('keydown', this._currentEscHandler);
                this._currentEscHandler = null;
            }
        };

        // 关闭按钮事件
        closeBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopImmediatePropagation();
            closeModal();
        });

        // 取消按钮事件
        cancelBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopImmediatePropagation();
            closeModal();
        });

        // 背景点击事件
        backdrop.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopImmediatePropagation();
            // 只有点击 backdrop 本身时才关闭
            if (e.target === backdrop) {
                closeModal();
            }
        });

        saveBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopImmediatePropagation();

            if (isClosing) return; // 防止重复处理

            const form = modal.querySelector('.task-form-redesigned');
            if (!form) {
                console.error('找不到表单元素');
                return;
            }
            const formData = new FormData(form);

            // 使用统一的数据提取方法
            const taskData = this.extractTaskDataFromForm(formData);

            // 验证数据
            if (!taskData.title) {
                this.showNotification({
                    title: '输入错误',
                    message: '请输入任务标题',
                    type: 'warning'
                });
                return;
            }

            try {
                console.log('保存任务数据:', taskData);
                if (isEdit) {
                    this.updateTask(task.id, taskData);
                    console.log('任务已更新:', task.id);
                    // 立即关闭模态框，时间显示更新由 handleTaskTimeUpdate 处理
                    closeModal();
                } else {
                    const newTask = this.createTask(taskData);
                    console.log('任务已创建:', newTask);
                    closeModal();
                }
            } catch (error) {
                console.error('保存任务失败:', error);
                this.showNotification({
                    title: '保存失败',
                    message: '保存任务失败，请重试',
                    type: 'error'
                });
            }
        });

        // ESC键关闭对话框
        const handleEscKey = (e) => {
            if (e.key === 'Escape' && !isClosing) {
                e.preventDefault();
                e.stopImmediatePropagation();
                closeModal();
                document.removeEventListener('keydown', handleEscKey);
                this._currentEscHandler = null;
            }
        };

        // 清理之前的ESC处理器
        if (this._currentEscHandler) {
            document.removeEventListener('keydown', this._currentEscHandler);
        }

        this._currentEscHandler = handleEscKey;
        document.addEventListener('keydown', handleEscKey);

        // 聚焦到标题输入框
        setTimeout(() => {
            const titleInput = modal.querySelector('#taskTitle');
            if (titleInput) {
                titleInput.focus();
            }
        }, 100);
    }
    
    // ==================== 数据持久化 ====================
    
    saveData() {
        return this.storage.saveAll({
            tasks: this.tasks,
            lists: this.lists,
            settings: this.settings,
            archivedTasks: this.archivedTasks
        });
    }
    
    loadSettings() {
        this.settings = this.storage.getSettings();
    }
    
    // ==================== 事件系统 ====================

    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 销毁 TodoManager（清理资源）
     */
    destroy() {
        // 停止倒计时更新
        this.stopCountdownUpdates();

        // 销毁提醒管理器
        if (this.reminderManager) {
            this.reminderManager.destroy();
            this.reminderManager = null;
        }

        // 清理事件监听器
        this.eventListeners.clear();

        // 清理 UI 元素
        if (this.todoContainer) {
            this.todoContainer.remove();
            this.todoContainer = null;
        }

        // 清理 ESC 键处理器
        if (this._currentEscHandler) {
            document.removeEventListener('keydown', this._currentEscHandler);
            this._currentEscHandler = null;
        }

        console.log('TodoManager 已销毁');
    }
}
